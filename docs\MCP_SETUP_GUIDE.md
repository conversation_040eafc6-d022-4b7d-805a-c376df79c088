# 🌍 全局 MCP 服务器配置指南

## ✅ 已验证可用的 MCP 服务器

### 1. 📝 Memory MCP (知识图谱)
- **命令**: `npx`
- **参数**: `["-y", "@modelcontextprotocol/server-memory"]`
- **作用域**: User (全局)
- **功能**: 存储和检索项目知识、策略分析结果

### 2. 📁 Filesystem MCP (文件系统)
- **命令**: `npx`
- **参数**: `["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop"]`
- **作用域**: User (全局)
- **功能**: 读写项目文件、数据文件管理

### 3. 🔄 Git MCP (版本控制)
- **命令**: `uvx`
- **参数**: `["mcp-server-git", "--repository", "."]`
- **作用域**: Project (项目级)
- **功能**: Git 仓库管理、版本控制操作

## 🚀 在 Claudia 中配置步骤

### 方法 1: 通过 Claudia GUI 配置

1. **打开 Claudia** (已启动在 http://localhost:1420/)
2. **进入 MCP 管理页面**
3. **添加 Memory MCP**:
   ```
   名称: memory
   命令: npx
   参数: -y @modelcontextprotocol/server-memory
   作用域: User
   ```

4. **添加 Filesystem MCP**:
   ```
   名称: filesystem
   命令: npx
   参数: -y @modelcontextprotocol/server-filesystem /Users/<USER>/Desktop
   作用域: User
   ```

5. **添加 Git MCP**:
   ```
   名称: git
   命令: uvx
   参数: mcp-server-git --repository .
   作用域: Project
   ```

### 方法 2: 通过配置文件

在项目根目录创建 `.mcp.json`:

```json
{
  "mcpServers": {
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"]
    },
    "filesystem": {
      "command": "npx", 
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop"]
    },
    "git": {
      "command": "uvx",
      "args": ["mcp-server-git", "--repository", "."]
    }
  }
}
```

## 🎯 量化交易项目专用配置

在 `/Users/<USER>/Desktop/quant011/` 中添加项目特定的 MCP:

```json
{
  "mcpServers": {
    "quant-python": {
      "command": "python3",
      "args": ["-m", "mcp_server_python"],
      "workingDirectory": "/Users/<USER>/Desktop/quant011"
    },
    "quant-data": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop/quant011/data"]
    }
  }
}
```

## ✅ 验证配置

配置完成后，在 Claudia 中应该能看到：
- ✅ 知识图谱存储能力 (Memory)
- ✅ 文件系统操作能力 (Filesystem)
- ✅ Git 仓库管理能力 (Git)
- ✅ 项目特定功能 (量化分析)

## 🔧 环境要求

- ✅ Node.js 22.14.0 (已安装)
- ✅ Python 3.13.5 (已安装)
- ✅ uv 0.8.4 (已安装)
- ✅ Claudia (已启动)

## 📝 使用说明

1. **全局 MCP** 在所有项目中可用
2. **项目 MCP** 仅在特定项目中可用
3. **Memory MCP** 可以跨项目共享知识
4. **Filesystem MCP** 限制在 Desktop 目录内
5. **Git MCP** 自动检测当前项目的 Git 仓库

## 🎉 配置完成

现在你可以在任何项目中使用这些 MCP 服务器功能！
