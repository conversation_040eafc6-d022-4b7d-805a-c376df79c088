# 🤖 BMad Method MCP Server 安装指南

## 📋 **问题分析**

### **🔍 当前状况**
- BMad Method MCP Server 需要完整的 CrewAI 依赖
- 系统环境：Python 3.13.5，pip 25.1.1
- 需要安装 CrewAI 和相关工具包

## 🛠️ **安装步骤**

### **第一步：安装 CrewAI 核心依赖**

```bash
# 安装 CrewAI 核心包
pip3 install crewai

# 安装 CrewAI 工具包
pip3 install crewai-tools

# 安装额外的依赖（如果需要）
pip3 install langchain langchain-community
```

### **第二步：安装 BMad Method MCP Server**

#### **方法 1：使用 uvx（推荐）**
```bash
# 如果 uvx 可用
uvx --from bmad-method-mcp-server bmad-method-mcp-server

# 或者直接运行
uvx bmad-method-mcp-server
```

#### **方法 2：使用 pip 安装**
```bash
# 直接安装 BMad Method MCP Server
pip3 install bmad-method-mcp-server

# 或者从 GitHub 安装（如果是开源项目）
pip3 install git+https://github.com/bmad-method/mcp-server.git
```

#### **方法 3：手动安装**
```bash
# 克隆仓库（如果可用）
git clone https://github.com/bmad-method/mcp-server.git
cd mcp-server
pip3 install -e .
```

### **第三步：验证安装**

```bash
# 检查 CrewAI 安装
python3 -c "import crewai; print('CrewAI 安装成功')"

# 检查 BMad Method MCP Server
bmad-method-mcp-server --help
```

### **第四步：添加到全局 MCP 配置**

```bash
# 添加到 Claude MCP 配置
claude mcp add-json -s user bmad-method '{"command": "uvx", "args": ["bmad-method-mcp-server"]}'

# 或者使用 Python 直接运行
claude mcp add-json -s user bmad-method '{"command": "python3", "args": ["-m", "bmad_method_mcp_server"]}'
```

## 🔧 **依赖要求**

### **🐍 Python 依赖**
```
crewai>=0.1.0
crewai-tools>=0.1.0
langchain>=0.1.0
langchain-community>=0.1.0
pydantic>=2.0.0
fastapi>=0.100.0
uvicorn>=0.20.0
```

### **🌐 环境变量（可能需要）**
```bash
export OPENAI_API_KEY="your-openai-api-key"
export ANTHROPIC_API_KEY="your-anthropic-api-key"
export CREWAI_API_KEY="your-crewai-api-key"
```

## 🎯 **BMad Method MCP Server 功能**

### **🤖 AI Agent 协作**
- **多 Agent 系统**: 创建和管理多个 AI Agent
- **任务分配**: 智能任务分配和协调
- **工作流管理**: 复杂工作流的自动化执行

### **📊 数据处理**
- **数据分析**: 自动化数据分析和报告
- **模型训练**: 机器学习模型的训练和优化
- **结果可视化**: 数据可视化和报告生成

### **🔄 集成能力**
- **API 集成**: 与外部 API 的无缝集成
- **数据库连接**: 多种数据库的连接和操作
- **文件处理**: 各种格式文件的处理和转换

## 🚀 **量化交易应用**

### **📈 交易策略开发**
- **策略回测**: 自动化策略回测和优化
- **风险管理**: 智能风险评估和管理
- **信号生成**: 交易信号的自动生成

### **📊 市场分析**
- **技术分析**: 自动化技术指标分析
- **基本面分析**: 财务数据的深度分析
- **情感分析**: 市场情感和新闻分析

### **🤖 自动化交易**
- **订单执行**: 智能订单执行和管理
- **仓位管理**: 动态仓位调整
- **监控报警**: 实时监控和报警系统

## 🔍 **故障排除**

### **常见问题**

#### **1. CrewAI 安装失败**
```bash
# 升级 pip
pip3 install --upgrade pip

# 使用国内镜像
pip3 install -i https://pypi.tuna.tsinghua.edu.cn/simple crewai
```

#### **2. 依赖冲突**
```bash
# 创建虚拟环境
python3 -m venv bmad_env
source bmad_env/bin/activate
pip install crewai crewai-tools
```

#### **3. 权限问题**
```bash
# 使用用户安装
pip3 install --user crewai crewai-tools
```

## 📝 **配置示例**

### **MCP 配置文件示例**
```json
{
  "mcpServers": {
    "bmad-method": {
      "command": "uvx",
      "args": ["bmad-method-mcp-server"],
      "env": {
        "OPENAI_API_KEY": "your-api-key",
        "CREWAI_API_KEY": "your-crewai-key"
      }
    }
  }
}
```

## 🎉 **完成后的功能**

### **🔥 增强的 MCP 生态系统**
一旦成功安装，你将拥有：

1. **🧠 Memory MCP** - 知识存储
2. **📁 Filesystem MCP** - 文件操作
3. **🔄 Git MCP** - 版本控制
4. **🌐 Puppeteer MCP** - Web 自动化
5. **🐙 GitHub MCP** - 代码仓库管理
6. **🌐 Chrome MCP** - 浏览器控制
7. **🗺️ Amap Maps MCP** - 地图服务
8. **🤖 BMad Method MCP** - AI Agent 协作

### **💡 建议**
1. **先安装 CrewAI 依赖**
2. **验证安装成功**
3. **配置环境变量**
4. **添加到全局 MCP 配置**
5. **在 Claudia 中测试功能**

**🚀 这将为你的量化交易平台提供强大的 AI Agent 协作能力！**
