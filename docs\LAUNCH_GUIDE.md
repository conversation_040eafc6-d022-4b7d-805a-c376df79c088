# 🚀 Claudia 快捷启动指南

本指南介绍了多种启动 Claudia 的便捷方式，包括普通模式和狂飙模式。

## 📋 启动方式总览

| 方式 | 普通模式 | 狂飙模式 | 平台 | 说明 |
|------|----------|----------|------|------|
| macOS App | `Claudia.app` | `Claudia Turbo.app` | macOS | 双击启动，可拖到 Dock |
| Shell 脚本 | `./start-claudia.sh` | `./start-claudia.sh turbo` | macOS/Linux | 终端运行 |
| 批处理文件 | `start-claudia.bat` | `start-claudia.bat turbo` | Windows | 双击或命令行运行 |
| npm 脚本 | `bun run start` | `bun run start:turbo` | 全平台 | 通过包管理器运行 |

## 🐢 普通模式 vs 🏎️ 狂飙模式

### 普通模式
- ✅ 完整的安全检查和权限验证
- ✅ 推荐用于生产环境
- ✅ 更安全，更稳定
- ⏱️ 启动时间稍长

### 狂飙模式 (`--dangerously-skip-permissions`)
- ⚡ 跳过权限检查，启动更快
- ⚠️ 安全性降低，仅建议开发时使用
- 🚨 **注意**: 使用前请确保了解相关风险
- 🏎️ 适合快速开发和测试

## 🖥️ 各平台使用方法

### macOS

#### 方法 1: 应用程序快捷方式（推荐）
```bash
# 双击以下文件即可启动
Claudia.app              # 普通模式
Claudia Turbo.app        # 狂飙模式
```

#### 方法 2: 终端脚本
```bash
# 普通模式
./start-claudia.sh

# 狂飙模式
./start-claudia.sh turbo

# 查看帮助
./start-claudia.sh help
```

#### 方法 3: npm 脚本
```bash
# 普通模式
bun run start

# 狂飙模式
bun run start:turbo
```

### Windows

#### 方法 1: 批处理文件（推荐）
```cmd
REM 双击 start-claudia.bat 或在命令行运行:

REM 普通模式
start-claudia.bat

REM 狂飙模式
start-claudia.bat turbo

REM 查看帮助
start-claudia.bat help
```

#### 方法 2: npm 脚本
```cmd
REM 普通模式
bun run start

REM 狂飙模式
bun run start:turbo
```

### Linux

#### 方法 1: Shell 脚本（推荐）
```bash
# 普通模式
./start-claudia.sh

# 狂飙模式
./start-claudia.sh turbo

# 查看帮助
./start-claudia.sh help
```

#### 方法 2: npm 脚本
```bash
# 普通模式
bun run start

# 狂飙模式
bun run start:turbo
```

## 🔧 自定义配置

### 添加到系统 PATH
您可以将启动脚本添加到系统 PATH 中，以便在任何位置启动：

```bash
# macOS/Linux
echo 'export PATH="$PATH:/path/to/claudia"' >> ~/.bashrc
# 或
echo 'export PATH="$PATH:/path/to/claudia"' >> ~/.zshrc

# 然后可以在任何位置运行:
start-claudia.sh turbo
```

### 创建桌面快捷方式
- **macOS**: 将 `.app` 文件拖到桌面或 Applications 文件夹
- **Windows**: 右键 `start-claudia.bat` → 发送到 → 桌面快捷方式
- **Linux**: 创建 `.desktop` 文件

## 🛠️ 故障排除

### 常见问题

1. **权限错误**
   ```bash
   chmod +x start-claudia.sh
   chmod +x create-app-shortcuts.sh
   ```

2. **依赖缺失**
   - 确保已安装 `bun`、`cargo` (Rust)
   - 可选: 安装 `claude` CLI

3. **端口占用**
   - 脚本会自动检测并询问是否停止现有实例

4. **macOS 安全警告**
   - 首次运行可能需要在"系统偏好设置" → "安全性与隐私"中允许

### 日志查看
启动时的详细日志会显示在终端中，包括：
- 依赖检查结果
- 编译进度
- 应用启动状态

## 📱 快速开始

1. **首次使用**:
   ```bash
   ./start-claudia.sh
   ```

2. **开发模式**:
   ```bash
   ./start-claudia.sh turbo
   ```

3. **创建快捷方式** (仅 macOS):
   ```bash
   ./create-app-shortcuts.sh
   ```

## 🎯 推荐工作流

1. **开发时**: 使用狂飙模式快速启动和测试
2. **演示时**: 使用普通模式确保稳定性
3. **日常使用**: 根据需要选择合适的模式

---

💡 **提示**: 所有启动方式都支持热重载，代码更改会自动反映在应用中。
