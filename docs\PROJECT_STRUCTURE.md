# Claudia 项目结构说明

## 📁 项目文件夹结构

```
claudia/
├── 📂 docs/                    # 📖 文档文件夹
│   ├── README.md              # 项目主要说明文档
│   ├── QUICK_START.md         # 快速开始指南
│   ├── LAUNCH_GUIDE.md        # 启动指南
│   ├── CONTRIBUTING.md        # 贡献指南
│   ├── Windows启动脚本使用说明.md # Windows 启动脚本说明
│   ├── MCP_SETUP_GUIDE.md     # MCP 设置指南
│   ├── MCP_CONFIGURATION_COMPLETE.md
│   ├── MCP_AUTO_CONFIGURATION_SUCCESS.md
│   ├── MCP_GRAPHITI_BMAD_DEPLOYMENT_SUCCESS.md
│   ├── PUPPETEER_MCP_DEPLOYMENT_SUCCESS.md
│   ├── EXTENDED_MCP_DEPLOYMENT_SUCCESS.md
│   ├── BMAD_METHOD_MCP_INSTALLATION_GUIDE.md
│   └── PROJECT_STRUCTURE.md   # 本文档
│
├── 📂 launchers/              # 🚀 启动脚本文件夹
│   ├── claudia-turbo.bat     # Windows 狂飙模式启动器 (推荐)
│   ├── claudia-normal.bat    # Windows 普通模式启动器 (推荐)
│   ├── 狂飙模式.bat          # 中文版狂飙模式 (可能有编码问题)
│   ├── 普通模式.bat          # 中文版普通模式 (可能有编码问题)
│   ├── 普通模式.ps1          # PowerShell 普通模式脚本
│   ├── start-claudia-turbo.bat
│   ├── start-claudia-normal.bat
│   ├── start-turbo.bat
│   ├── start-normal.bat
│   ├── start-claudia.bat
│   ├── start-claudia.sh      # macOS/Linux 启动脚本
│   └── create-app-shortcuts.sh # 创建应用快捷方式脚本
│
├── 📂 mcp-configs/           # ⚙️ MCP 配置文件夹
│   ├── basic_mcp_config.json # 基础 MCP 配置
│   ├── mcp-global-config.json # 全局 MCP 配置
│   └── graphiti_mcp_config.json # Graphiti MCP 配置
│
├── 📂 scripts/               # 🔧 脚本工具文件夹
│   ├── test_mcp_servers.sh   # MCP 服务器测试脚本
│   └── test_new_mcp_servers.sh # 新 MCP 服务器测试脚本
│
├── 📂 build-outputs/         # 📦 构建输出文件夹
│   ├── dist/                 # Vite 构建输出
│   ├── Claudia.app/          # macOS 应用包
│   └── Claudia Turbo.app/    # macOS 狂飙模式应用包
│
├── 📂 src/                   # 💻 前端源代码
│   ├── App.tsx               # 主应用组件
│   ├── main.tsx              # 应用入口
│   ├── styles.css            # 全局样式
│   ├── vite-env.d.ts         # Vite 类型定义
│   ├── assets/               # 静态资源
│   ├── components/           # React 组件
│   ├── contexts/             # React 上下文
│   ├── hooks/                # 自定义 Hooks
│   ├── lib/                  # 工具库
│   ├── stores/               # 状态管理
│   └── types/                # TypeScript 类型定义
│
├── 📂 src-tauri/             # 🦀 Tauri 后端代码
│   ├── src/                  # Rust 源代码
│   ├── Cargo.toml            # Rust 项目配置
│   ├── Cargo.lock            # Rust 依赖锁定
│   ├── tauri.conf.json       # Tauri 配置
│   ├── build.rs              # 构建脚本
│   ├── capabilities/         # Tauri 权限配置
│   ├── gen/                  # 生成的代码
│   ├── icons/                # 应用图标
│   ├── target/               # Rust 编译输出
│   └── tests/                # 测试文件
│
├── 📂 cc_agents/             # 🤖 Claude Code 代理配置
│   ├── README.md
│   ├── git-commit-bot.claudia.json
│   ├── security-scanner.claudia.json
│   └── unit-tests-bot.claudia.json
│
├── 📂 graphiti/              # 🕸️ Graphiti MCP 服务器
│   ├── README.md
│   ├── mcp_server/           # MCP 服务器实现
│   ├── graphiti_core/        # 核心功能
│   ├── examples/             # 示例代码
│   ├── tests/                # 测试文件
│   └── ...                   # 其他 Graphiti 相关文件
│
├── 📂 puppeteer-mcp-server/  # 🎭 Puppeteer MCP 服务器
│   ├── index.js              # 服务器主文件
│   └── package.json          # Node.js 项目配置
│
├── 📂 public/                # 🌐 公共静态文件
│   ├── tauri.svg
│   └── vite.svg
│
├── 📂 node_modules/          # 📚 Node.js 依赖包
│
├── package.json              # 📋 Node.js 项目配置
├── package-lock.json         # Node.js 依赖锁定
├── bun.lock                  # Bun 依赖锁定
├── tsconfig.json             # TypeScript 配置
├── tsconfig.node.json        # Node.js TypeScript 配置
├── vite.config.ts            # Vite 配置
├── index.html                # HTML 入口文件
└── LICENSE                   # 许可证文件
```

## 🎯 文件夹用途说明

### 📖 docs/ - 文档文件夹
存放所有项目相关的文档，包括：
- 用户指南和快速开始文档
- 开发者贡献指南
- MCP 配置和部署文档
- Windows 启动脚本使用说明

### 🚀 launchers/ - 启动脚本文件夹
存放各种平台的启动脚本：
- **推荐使用**: `claudia-turbo.bat` 和 `claudia-normal.bat`
- Windows 批处理文件 (.bat)
- PowerShell 脚本 (.ps1)
- macOS/Linux Shell 脚本 (.sh)

### ⚙️ mcp-configs/ - MCP 配置文件夹
存放 Model Context Protocol 相关配置：
- 基础配置文件
- 全局配置文件
- 特定服务器配置文件

### 🔧 scripts/ - 脚本工具文件夹
存放开发和测试相关的脚本：
- MCP 服务器测试脚本
- 自动化工具脚本

### 📦 build-outputs/ - 构建输出文件夹
存放编译和构建生成的文件：
- Vite 前端构建输出
- Tauri 应用包
- 分发用的可执行文件

## 🚀 快速启动

### Windows 用户
1. 进入 `launchers/` 文件夹
2. 双击 `claudia-turbo.bat` (狂飙模式) 或 `claudia-normal.bat` (普通模式)

### macOS/Linux 用户
1. 进入 `launchers/` 文件夹
2. 运行 `./start-claudia.sh`

## 📝 注意事项

1. **启动脚本**: 推荐使用 `claudia-turbo.bat` 和 `claudia-normal.bat`，这两个文件经过优化，避免了编码问题
2. **MCP 配置**: 所有 MCP 相关配置文件已移动到 `mcp-configs/` 文件夹
3. **文档查阅**: 所有文档都在 `docs/` 文件夹中，按需查阅
4. **构建输出**: 构建生成的文件在 `build-outputs/` 文件夹中，不要手动修改

## 🔄 更新说明

此文件夹结构于 2025年8月2日 重新整理，旨在提高项目的可维护性和用户体验。
