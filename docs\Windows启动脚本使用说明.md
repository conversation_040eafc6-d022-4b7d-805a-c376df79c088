# Claudia Windows 启动脚本使用说明

## 📁 文件说明

我已经为您创建了 Windows 版本的 Claudia 启动脚本，包含以下文件：

### 🏎️ 狂飙模式 (跳过权限检查)
- **狂飙模式.bat** - Windows 批处理文件版本 (中文界面，可能有编码问题)
- **start-claudia-turbo.bat** - 英文版批处理文件 (推荐)
- **普通模式.ps1** - PowerShell 脚本版本

### 🐢 普通模式 (标准安全检查)
- **普通模式.bat** - Windows 批处理文件版本 (中文界面，可能有编码问题)
- **start-claudia-normal.bat** - 英文版批处理文件 (推荐)
- **普通模式.ps1** - PowerShell 脚本版本

## 🚀 使用方法

### 方法 1: 双击运行 (.bat 文件) - 推荐
1. 直接双击 `start-claudia-turbo.bat` (狂飙模式) 或 `start-claudia-normal.bat` (普通模式)
2. 系统会自动使用命令提示符运行脚本
3. 英文版本避免了中文编码问题，更稳定

### 方法 2: PowerShell 运行 (.ps1 文件)
1. 右键点击 `普通模式.ps1`
2. 选择 "使用 PowerShell 运行"
3. 如果提示执行策略限制，请先运行：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

### 方法 3: 命令行运行
```cmd
# 使用命令提示符 (推荐)
start-claudia-turbo.bat
start-claudia-normal.bat

# 使用 PowerShell
powershell -ExecutionPolicy Bypass -File "普通模式.ps1"

# 在 PowerShell 中运行 .bat 文件
.\start-claudia-turbo.bat
.\start-claudia-normal.bat
```

## 🔧 功能特性

### ✅ 自动检查
- **系统依赖检查**: Bun、Rust/Cargo、Claude CLI
- **运行实例检测**: 自动检测已运行的 Claudia 实例
- **依赖安装**: 自动安装前端依赖 (如果需要)

### 🎨 用户体验
- **彩色输出**: PowerShell 版本支持彩色文本显示
- **UTF-8 编码**: 正确显示中文字符和 Emoji
- **错误处理**: 完善的错误提示和处理机制
- **交互式操作**: 支持用户选择和确认

### 🏎️ 狂飙模式 vs 🐢 普通模式

| 特性 | 狂飙模式 | 普通模式 |
|------|----------|----------|
| 启动命令 | `bun run start:turbo` | `bun run start` |
| 权限检查 | ⚡ 跳过 | 🔒 完整检查 |
| 启动速度 | 🏎️ 更快 | 🐢 标准 |
| 安全性 | ⚠️ 开发用 | ✅ 生产推荐 |
| 适用场景 | 开发调试 | 日常使用 |

## 🛠️ 故障排除

### 问题 1: PowerShell 执行策略限制
**错误**: "无法加载文件，因为在此系统上禁止运行脚本"

**解决方案**:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 问题 2: 找不到 Bun/Rust/Claude
**错误**: "未找到 bun/cargo/claude"

**解决方案**:
1. **Bun**: 访问 https://bun.sh/ 安装
2. **Rust**: 访问 https://rustup.rs/ 安装
3. **Claude CLI**: 访问 https://claude.ai/ 安装

### 问题 3: 中文显示乱码
**解决方案**: 使用 PowerShell 版本 (.ps1)，它会自动设置 UTF-8 编码

### 问题 4: 端口被占用
**错误**: 启动失败，端口 1420 被占用

**解决方案**:
1. 脚本会自动检测并询问是否停止现有实例
2. 选择 "y" 停止现有实例并重新启动

## 📝 注意事项

1. **首次运行**: 可能需要下载和安装依赖，请耐心等待
2. **网络连接**: 确保网络连接正常，用于下载 npm 包
3. **防火墙**: 可能需要允许 Claudia 通过防火墙
4. **管理员权限**: 通常不需要管理员权限，但某些系统可能需要

## 🎯 推荐使用

- **日常使用**: 推荐使用 `普通模式.ps1`
- **开发调试**: 推荐使用 `狂飙模式.ps1`
- **系统兼容**: 如果 PowerShell 有问题，使用对应的 `.bat` 文件

## 🔗 相关链接

- [Claudia 项目主页](https://github.com/your-repo/claudia)
- [Bun 官网](https://bun.sh/)
- [Rust 官网](https://rustup.rs/)
- [Claude CLI 文档](https://claude.ai/)

---

**享受使用 Claudia！** 🎉
