{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ecommerce Runner\n", "\n", "This notebook is the Jupyter equivalent of the `runner.py` script."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "import logging\n", "import os\n", "import sys\n", "from datetime import datetime, timezone\n", "from pathlib import Path\n", "\n", "from dotenv import load_dotenv\n", "from rich.pretty import pprint\n", "\n", "from graphiti_core import Graphiti\n", "from graphiti_core.edges import EntityEdge\n", "from graphiti_core.llm_client.anthropic_client import AnthropicClient\n", "from graphiti_core.nodes import EpisodeType\n", "from graphiti_core.utils.bulk_utils import RawEpisode\n", "from graphiti_core.utils.maintenance.graph_data_operations import clear_data\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')\n", "neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')\n", "neo4j_password = os.environ.get('NEO4J_PASSWORD', 'password')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def setup_logging():\n", "    logger = logging.getLogger()\n", "    logger.setLevel(logging.INFO)\n", "    console_handler = logging.StreamHandler(sys.stdout)\n", "    console_handler.setLevel(logging.INFO)\n", "    formatter = logging.Formatter('%(name)s - %(levelname)s - %(message)s')\n", "    console_handler.setFormatter(formatter)\n", "    logger.addHandler(console_handler)\n", "    return logger\n", "\n", "\n", "logger = setup_logging()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["shoe_conversation_1 = [\n", "    \"SalesBot (2024-07-30T00:00:00Z): Hi, I'm ManyB<PERSON>ds Assistant! How can I help you today?\",\n", "    \"<PERSON> (2024-07-30T00:01:00Z): Hi, I'm looking for a new pair of shoes.\",\n", "    'SalesBot (2024-07-30T00:02:00Z): Of course! What kind of material are you looking for?',\n", "    \"<PERSON> (2024-07-30T00:03:00Z): I'm allergic to wool. Also, I'm a size 10 if that helps?\",\n", "    \"SalesBot (2024-07-30T00:04:00Z): We have just what you are looking for, how do you like our Men's Couriers. They have a retro silhouette look and from cotton. How about them in Basin Blue?\",\n", "    \"<PERSON> (2024-07-30T00:05:00Z): Blue is great! Love the look. I'll take them.\",\n", "]\n", "\n", "shoe_conversation_2 = [\n", "    'SalesBot (2024-08-20T00:00:00Z): Hi <PERSON>, how can I assist you today?',\n", "    \"John (2024-08-20T00:01:00Z): Hi, I need to return the Men's Couriers I bought recently. They're too tight for my wide feet. Hahaha.\",\n", "    \"SalesBot (2024-08-20T00:02:00Z): I'm sorry to hear that. We can process the return for you.\",\n", "]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["async def add_messages(client: <PERSON><PERSON><PERSON><PERSON>, messages: list[str], prefix: str = 'Message'):\n", "    for i, message in enumerate(messages):\n", "        await client.add_episode(\n", "            name=f'{prefix}-{i}',\n", "            episode_body=message,\n", "            source=EpisodeType.message,\n", "            reference_time=datetime.now(timezone.utc),\n", "            source_description='Shoe conversation',\n", "        )"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["async def ingest_products_data(client: Graphiti):\n", "    script_dir = Path.cwd().parent\n", "    json_file_path = script_dir / 'data' / 'manybirds_products.json'\n", "\n", "    with open(json_file_path) as file:\n", "        products = json.load(file)['products']\n", "\n", "    episodes: list[RawEpisode] = [\n", "        RawEpisode(\n", "            name=product.get('title', f'Product {i}'),\n", "            content=str({k: v for k, v in product.items() if k != 'images'}),\n", "            source_description='ManyBirds products',\n", "            source=EpisodeType.json,\n", "            reference_time=datetime.now(timezone.utc),\n", "        )\n", "        for i, product in enumerate(products)\n", "    ]\n", "\n", "    await client.add_episode_bulk(episodes)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def pretty_print(entity: EntityEdge | list[EntityEdge]):\n", "    if isinstance(entity, EntityEdge):\n", "        data = {k: v for k, v in entity.model_dump().items() if k != 'fact_embedding'}\n", "    elif isinstance(entity, list):\n", "        data = [{k: v for k, v in e.model_dump().items() if k != 'fact_embedding'} for e in entity]\n", "    else:\n", "        pprint(entity)\n", "        return\n", "    pprint(data)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["llm_client = AnthropicClient(cache=False)\n", "\n", "client = Graphiti(\n", "    neo4j_uri,\n", "    neo4j_user,\n", "    neo4j_password,\n", "    llm_client=llm_client,\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX entity_uuid IF NOT EXISTS FOR (e:Entity) ON (e.uuid)` has no effect.} {description: `RANGE INDEX entity_uuid FOR (e:Entity) ON (e.uuid)` already exists.} {position: None} for query: 'CREATE INDEX entity_uuid IF NOT EXISTS FOR (n:Entity) ON (n.uuid)'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX name_entity_index IF NOT EXISTS FOR (e:Entity) ON (e.name)` has no effect.} {description: `RANGE INDEX name_entity_index FOR (e:Entity) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX name_entity_index IF NOT EXISTS FOR (n:Entity) ON (n.name)'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX valid_at_episodic_index IF NOT EXISTS FOR (e:Episodic) ON (e.valid_at)` has no effect.} {description: `RANGE INDEX valid_at_episodic_index FOR (e:Episodic) ON (e.valid_at)` already exists.} {position: None} for query: 'CREATE INDEX valid_at_episodic_index IF NOT EXISTS FOR (n:Episodic) ON (n.valid_at)'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX relation_uuid IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON (e.uuid)` has no effect.} {description: `RANGE INDEX relation_uuid FOR ()-[e:RELATES_TO]-() ON (e.uuid)` already exists.} {position: None} for query: 'CREATE INDEX relation_uuid IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON (e.uuid)'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE FULLTEXT INDEX name_and_fact IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON EACH [e.name, e.fact]` has no effect.} {description: `FULLTEXT INDEX name_and_fact FOR ()-[e:RELATES_TO]-() ON EACH [e.name, e.fact]` already exists.} {position: None} for query: 'CREATE FULLTEXT INDEX name_and_fact IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON EACH [e.name, e.fact]'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX created_at_episodic_index IF NOT EXISTS FOR (e:Episodic) ON (e.created_at)` has no effect.} {description: `RANGE INDEX created_at_episodic_index FOR (e:Episodic) ON (e.created_at)` already exists.} {position: None} for query: 'CREATE INDEX created_at_episodic_index IF NOT EXISTS FOR (n:Episodic) ON (n.created_at)'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX episode_uuid IF NOT EXISTS FOR (e:Episodic) ON (e.uuid)` has no effect.} {description: `RANGE INDEX episode_uuid FOR (e:Episodic) ON (e.uuid)` already exists.} {position: None} for query: 'CREATE INDEX episode_uuid IF NOT EXISTS FOR (n:Episodic) ON (n.uuid)'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE FULLTEXT INDEX name_and_summary IF NOT EXISTS FOR (e:Entity) ON EACH [e.name, e.summary]` has no effect.} {description: `FULLTEXT INDEX name_and_summary FOR (e:Entity) ON EACH [e.name, e.summary]` already exists.} {position: None} for query: 'CREATE FULLTEXT INDEX name_and_summary IF NOT EXISTS FOR (n:Entity) ON EACH [n.name, n.summary]'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX valid_at_edge_index IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON (e.valid_at)` has no effect.} {description: `RANGE INDEX valid_at_edge_index FOR ()-[e:RELATES_TO]-() ON (e.valid_at)` already exists.} {position: None} for query: 'CREATE INDEX valid_at_edge_index IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON (e.valid_at)'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX name_edge_index IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON (e.name)` has no effect.} {description: `RANGE INDEX name_edge_index FOR ()-[e:RELATES_TO]-() ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX name_edge_index IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON (e.name)'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX mention_uuid IF NOT EXISTS FOR ()-[e:MENTIONS]-() ON (e.uuid)` has no effect.} {description: `RANGE INDEX mention_uuid FOR ()-[e:MENTIONS]-() ON (e.uuid)` already exists.} {position: None} for query: 'CREATE INDEX mention_uuid IF NOT EXISTS FOR ()-[e:MENTIONS]-() ON (e.uuid)'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX created_at_edge_index IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON (e.created_at)` has no effect.} {description: `RANGE INDEX created_at_edge_index FOR ()-[e:RELATES_TO]-() ON (e.created_at)` already exists.} {position: None} for query: 'CREATE INDEX created_at_edge_index IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON (e.created_at)'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX invalid_at_edge_index IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON (e.invalid_at)` has no effect.} {description: `RANGE INDEX invalid_at_edge_index FOR ()-[e:RELATES_TO]-() ON (e.invalid_at)` already exists.} {position: None} for query: 'CREATE INDEX invalid_at_edge_index IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON (e.invalid_at)'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX expired_at_edge_index IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON (e.expired_at)` has no effect.} {description: `RANGE INDEX expired_at_edge_index FOR ()-[e:RELATES_TO]-() ON (e.expired_at)` already exists.} {position: None} for query: 'CREATE INDEX expired_at_edge_index IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON (e.expired_at)'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE VECTOR INDEX fact_embedding IF NOT EXISTS FOR ()-[e:RELATES_TO]-() ON (e.fact_embedding) OPTIONS {indexConfig: {`vector.dimensions`: 1024, `vector.similarity_function`: \"cosine\"}}` has no effect.} {description: `VECTOR INDEX fact_embedding FOR ()-[e:RELATES_TO]-() ON (e.fact_embedding)` already exists.} {position: None} for query: \"\\n        CREATE VECTOR INDEX fact_embedding IF NOT EXISTS\\n        FOR ()-[r:RELATES_TO]-() ON (r.fact_embedding)\\n        OPTIONS {indexConfig: {\\n         `vector.dimensions`: 1024,\\n         `vector.similarity_function`: 'cosine'\\n        }}\\n        \"\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX created_at_entity_index IF NOT EXISTS FOR (e:Entity) ON (e.created_at)` has no effect.} {description: `RANGE INDEX created_at_entity_index FOR (e:Entity) ON (e.created_at)` already exists.} {position: None} for query: 'CREATE INDEX created_at_entity_index IF NOT EXISTS FOR (n:Entity) ON (n.created_at)'\n", "neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE VECTOR INDEX name_embedding IF NOT EXISTS FOR (e:Entity) ON (e.name_embedding) OPTIONS {indexConfig: {`vector.dimensions`: 1024, `vector.similarity_function`: \"cosine\"}}` has no effect.} {description: `VECTOR INDEX name_embedding FOR (e:Entity) ON (e.name_embedding)` already exists.} {position: None} for query: \"\\n        CREATE VECTOR INDEX name_embedding IF NOT EXISTS\\n        FOR (n:Entity) ON (n.name_embedding)\\n        OPTIONS {indexConfig: {\\n         `vector.dimensions`: 1024,\\n         `vector.similarity_function`: 'cosine'\\n        }}\\n        \"\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: c7f2523189804f6383d9ace08a7aaf37\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 697db68b36fa4e3987979c0cbc9f9f17\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 284d33cb75004a9e9fea6228ecfcba1d\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 097aaab533904f3d879b339e7f324be9\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 4a302ac072c94f9da876535b1130e03d\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': 'Anytime No Show Sock - Rugged Beige', 'labels': ['Entity', 'Product'], 'summary': 'A lightweight, breathable sock product by Manybirds'}, {'name': 'Manybirds', 'labels': ['Entity', 'Brand'], 'summary': 'The vendor and brand of the sock product'}, {'name': 'Socks', 'labels': ['Entity', 'ProductType'], 'summary': 'The category of the product'}] in 2819.064140319824 ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Anytime No Show Sock - Rugged Beige (UUID: 29db0ed04db44b0da0316b277e170aed)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Manybirds (UUID: 45db2d71977a40219557ba76ff507b7c)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Socks (UUID: 8169219a1c564a53a7201bf215bd45f8)\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': \"Women's Tree Breezers Knit - Rugged Beige\", 'labels': ['Entity', 'Product'], 'summary': \"A women's ballet flat shoe product by Many<PERSON>\"}, {'name': 'Manybirds', 'labels': ['Entity', 'Brand'], 'summary': 'The brand that produces the Tree Breezers shoe'}, {'name': 'Tree Breezer', 'labels': ['Entity', 'ProductLine'], 'summary': 'A specific line of shoes characterized by eucalyptus tree fiber material'}] in 3390.************ ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Women's Tree Breezers Knit - Rugged Beige (UUID: 28f10c5ba8824097b3517dd2ee40ffef)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Many<PERSON> (UUID: 6cecc29921234ed7a9d099cb5239c071)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: <PERSON> Breezer (UUID: 7d49a3b6bb4249f7a1262fbfbe6386b0)\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': \"Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole)\", 'labels': ['Entity', 'Product'], 'summary': \"A lightweight men's running shoe product\"}, {'name': 'Manybirds', 'labels': ['Entity', 'Brand'], 'summary': 'The brand that produces the SuperLight Wool Runners'}, {'name': 'SuperLight Wool Runner', 'labels': ['Entity', 'ProductLine'], 'summary': 'A specific line of lightweight running shoes'}, {'name': 'SuperLight Foam', 'labels': ['Entity', 'Technology'], 'summary': 'Revolutionary foam technology used in the shoe'}] in 3470.************ ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) (UUID: 0e96a1b72fe145a79ec2b36842ac6fd9)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Manybirds (UUID: 1a06474d3ce24fee9348fca1b47563a8)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: SuperLight Wool Runner (UUID: ce912ca620e247f4a0e9fe92aed41a1b)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: SuperLight Foam (UUID: 24c2e745740c4ba8bc75e60f51cf2865)\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': 'TinyBirds Wool Runners', 'labels': ['Entity', 'Product'], 'summary': 'Eco-friendly and machine washable sneakers for kids made with ZQ Merino Wool'}, {'name': 'Manybirds', 'labels': ['Entity', 'Brand'], 'summary': 'Manufacturer of TinyBirds Wool Runners'}, {'name': 'Natural Black', 'labels': ['Entity', 'Color'], 'summary': 'Color variant of the TinyBirds Wool Runners'}, {'name': 'Blizzard Sole', 'labels': ['Entity', 'ProductFeature'], 'summary': 'Specific sole type for the TinyBirds Wool Runners'}] in 3613.6529445648193 ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: TinyBirds Wool Runners (UUID: 138a288fc46f40a18623ccf970d49813)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Manybirds (UUID: 0553a72ef65e41999d20a0ffee0b4880)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: <PERSON> Black (UUID: e4cadcacd02f42e4b620721dba42bc9a)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Blizzard Sole (UUID: 0b63349f5a3342f1a87be29f316300f1)\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole)\", 'labels': ['Entity', 'Product'], 'summary': \"A men's shoe product from ManyBirds\"}, {'name': 'Manybirds', 'labels': ['Entity', 'Brand'], 'summary': 'The brand that produces the shoe product'}, {'name': 'Shoes', 'labels': ['Entity', 'ProductType'], 'summary': 'The type of product being described'}, {'name': 'Runner', 'labels': ['Entity', 'Silhouette'], 'summary': 'The style or silhouette of the shoe'}, {'name': 'Cotton', 'labels': ['Entity', 'Material'], 'summary': 'One of the materials used in the product'}] in 4271.************ ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) (UUID: ed9688ba1e9940ff87d3e26bcf5d7ae4)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Manybirds (UUID: 01ec048c30444e84b0e74a9bed35033d)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Shoes (UUID: 77f8b23b74014a7f85fffa0067dbf815)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: <PERSON> (UUID: 95066726921c4e5883a86d8095cd7e0a)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: <PERSON> (UUID: b9fb205d2511491b83061c432b3f9bf2)\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'MANUFACTURED_BY', 'source_node_uuid': '29db0ed04db44b0da0316b277e170aed', 'target_node_uuid': '45db2d71977a40219557ba76ff507b7c', 'fact': 'The Anytime No Show Sock - Rugged Beige is manufactured by Manybirds', 'valid_at': None, 'invalid_at': None}, {'relation_type': 'BELONGS_TO_CATEGORY', 'source_node_uuid': '29db0ed04db44b0da0316b277e170aed', 'target_node_uuid': '8169219a1c564a53a7201bf215bd45f8', 'fact': 'The Anytime No Show Sock - Rugged Beige belongs to the Socks category', 'valid_at': None, 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'MANUFACTURED_BY', 'source_node_uuid': '29db0ed04db44b0da0316b277e170aed', 'target_node_uuid': '45db2d71977a40219557ba76ff507b7c', 'fact': 'The Anytime No Show Sock - Rugged Beige is manufactured by Manybirds', 'valid_at': None, 'invalid_at': None}, {'relation_type': 'BELONGS_TO_CATEGORY', 'source_node_uuid': '29db0ed04db44b0da0316b277e170aed', 'target_node_uuid': '8169219a1c564a53a7201bf215bd45f8', 'fact': 'The Anytime No Show Sock - Rugged Beige belongs to the Socks category', 'valid_at': None, 'invalid_at': None}] in 5150.070905685425 ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: MANUFACTURED_BY from (UUID: 29db0ed04db44b0da0316b277e170aed) to (UUID: 45db2d71977a40219557ba76ff507b7c)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: BELONGS_TO_CATEGORY from (UUID: 29db0ed04db44b0da0316b277e170aed) to (UUID: 8169219a1c564a53a7201bf215bd45f8)\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'IS_PRODUCT_OF', 'source_node_uuid': '28f10c5ba8824097b3517dd2ee40ffef', 'target_node_uuid': '6cecc29921234ed7a9d099cb5239c071', 'fact': \"The Women's Tree Breezers Knit - Rugged Beige is a product made by Manybirds\", 'valid_at': None, 'invalid_at': None}, {'relation_type': 'IS_VARIANT_OF', 'source_node_uuid': '28f10c5ba8824097b3517dd2ee40ffef', 'target_node_uuid': '7d49a3b6bb4249f7a1262fbfbe6386b0', 'fact': \"The Women's Tree Breezers Knit - Rugged Beige is a specific variant of the Tree Breezer line\", 'valid_at': None, 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'IS_PRODUCT_OF', 'source_node_uuid': '28f10c5ba8824097b3517dd2ee40ffef', 'target_node_uuid': '6cecc29921234ed7a9d099cb5239c071', 'fact': \"The Women's Tree Breezers Knit - Rugged Beige is a product made by Manybirds\", 'valid_at': None, 'invalid_at': None}, {'relation_type': 'IS_VARIANT_OF', 'source_node_uuid': '28f10c5ba8824097b3517dd2ee40ffef', 'target_node_uuid': '7d49a3b6bb4249f7a1262fbfbe6386b0', 'fact': \"The Women's Tree Breezers Knit - Rugged Beige is a specific variant of the Tree Breezer line\", 'valid_at': None, 'invalid_at': None}] in 5457.************ ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: IS_PRODUCT_OF from (UUID: 28f10c5ba8824097b3517dd2ee40ffef) to (UUID: 6cecc29921234ed7a9d099cb5239c071)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: IS_VARIANT_OF from (UUID: 28f10c5ba8824097b3517dd2ee40ffef) to (UUID: 7d49a3b6bb4249f7a1262fbfbe6386b0)\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'MANUFACTURED_BY', 'source_node_uuid': '138a288fc46f40a18623ccf970d49813', 'target_node_uuid': '0553a72ef65e41999d20a0ffee0b4880', 'fact': 'TinyBirds Wool Runners are manufactured by Manybirds', 'valid_at': None, 'invalid_at': None}, {'relation_type': 'HAS_COLOR_VARIANT', 'source_node_uuid': '138a288fc46f40a18623ccf970d49813', 'target_node_uuid': 'e4cadcacd02f42e4b620721dba42bc9a', 'fact': 'TinyBirds Wool Runners are available in Natural Black color', 'valid_at': None, 'invalid_at': None}, {'relation_type': 'HAS_SOLE_TYPE', 'source_node_uuid': '138a288fc46f40a18623ccf970d49813', 'target_node_uuid': '0b63349f5a3342f1a87be29f316300f1', 'fact': 'TinyBirds Wool Runners feature a Blizzard Sole', 'valid_at': None, 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'MANUFACTURED_BY', 'source_node_uuid': '138a288fc46f40a18623ccf970d49813', 'target_node_uuid': '0553a72ef65e41999d20a0ffee0b4880', 'fact': 'TinyBirds Wool Runners are manufactured by Manybirds', 'valid_at': None, 'invalid_at': None}, {'relation_type': 'HAS_COLOR_VARIANT', 'source_node_uuid': '138a288fc46f40a18623ccf970d49813', 'target_node_uuid': 'e4cadcacd02f42e4b620721dba42bc9a', 'fact': 'TinyBirds Wool Runners are available in Natural Black color', 'valid_at': None, 'invalid_at': None}, {'relation_type': 'HAS_SOLE_TYPE', 'source_node_uuid': '138a288fc46f40a18623ccf970d49813', 'target_node_uuid': '0b63349f5a3342f1a87be29f316300f1', 'fact': 'TinyBirds Wool Runners feature a Blizzard Sole', 'valid_at': None, 'invalid_at': None}] in 6267.147064208984 ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: MANUFACTURED_BY from (UUID: 138a288fc46f40a18623ccf970d49813) to (UUID: 0553a72ef65e41999d20a0ffee0b4880)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: HAS_COLOR_VARIANT from (UUID: 138a288fc46f40a18623ccf970d49813) to (UUID: e4cadcacd02f42e4b620721dba42bc9a)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: HAS_SOLE_TYPE from (UUID: 138a288fc46f40a18623ccf970d49813) to (UUID: 0b63349f5a3342f1a87be29f316300f1)\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'PRODUCED_BY', 'source_node_uuid': '0e96a1b72fe145a79ec2b36842ac6fd9', 'target_node_uuid': '1a06474d3ce24fee9348fca1b47563a8', 'fact': \"The Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) are produced by Manybirds\", 'valid_at': None, 'invalid_at': None}, {'relation_type': 'IS_VARIANT_OF', 'source_node_uuid': '0e96a1b72fe145a79ec2b36842ac6fd9', 'target_node_uuid': 'ce912ca620e247f4a0e9fe92aed41a1b', 'fact': \"The Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) is a specific variant of the SuperLight Wool Runner line\", 'valid_at': None, 'invalid_at': None}, {'relation_type': 'USES_TECHNOLOGY', 'source_node_uuid': '0e96a1b72fe145a79ec2b36842ac6fd9', 'target_node_uuid': '24c2e745740c4ba8bc75e60f51cf2865', 'fact': \"The Men's SuperLight Wool Runners use SuperLight Foam technology for a barely-there feel\", 'valid_at': None, 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'PRODUCED_BY', 'source_node_uuid': '0e96a1b72fe145a79ec2b36842ac6fd9', 'target_node_uuid': '1a06474d3ce24fee9348fca1b47563a8', 'fact': \"The Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) are produced by Manybirds\", 'valid_at': None, 'invalid_at': None}, {'relation_type': 'IS_VARIANT_OF', 'source_node_uuid': '0e96a1b72fe145a79ec2b36842ac6fd9', 'target_node_uuid': 'ce912ca620e247f4a0e9fe92aed41a1b', 'fact': \"The Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) is a specific variant of the SuperLight Wool Runner line\", 'valid_at': None, 'invalid_at': None}, {'relation_type': 'USES_TECHNOLOGY', 'source_node_uuid': '0e96a1b72fe145a79ec2b36842ac6fd9', 'target_node_uuid': '24c2e745740c4ba8bc75e60f51cf2865', 'fact': \"The Men's SuperLight Wool Runners use SuperLight Foam technology for a barely-there feel\", 'valid_at': None, 'invalid_at': None}] in 7733.************ ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: PRODUCED_BY from (UUID: 0e96a1b72fe145a79ec2b36842ac6fd9) to (UUID: 1a06474d3ce24fee9348fca1b47563a8)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: IS_VARIANT_OF from (UUID: 0e96a1b72fe145a79ec2b36842ac6fd9) to (UUID: ce912ca620e247f4a0e9fe92aed41a1b)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: USES_TECHNOLOGY from (UUID: 0e96a1b72fe145a79ec2b36842ac6fd9) to (UUID: 24c2e745740c4ba8bc75e60f51cf2865)\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'PRODUCED_BY', 'source_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'target_node_uuid': '01ec048c30444e84b0e74a9bed35033d', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is produced by Manybirds\", 'valid_at': None, 'invalid_at': None}, {'relation_type': 'IS_A', 'source_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'target_node_uuid': '77f8b23b74014a7f85fffa0067dbf815', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is a type of Shoes\", 'valid_at': None, 'invalid_at': None}, {'relation_type': 'HAS_STYLE', 'source_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'target_node_uuid': '95066726921c4e5883a86d8095cd7e0a', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) has a Runner style\", 'valid_at': None, 'invalid_at': None}, {'relation_type': 'MADE_OF', 'source_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'target_node_uuid': 'b9fb205d2511491b83061c432b3f9bf2', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is made of Cotton\", 'valid_at': None, 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'PRODUCED_BY', 'source_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'target_node_uuid': '01ec048c30444e84b0e74a9bed35033d', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is produced by Manybirds\", 'valid_at': None, 'invalid_at': None}, {'relation_type': 'IS_A', 'source_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'target_node_uuid': '77f8b23b74014a7f85fffa0067dbf815', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is a type of Shoes\", 'valid_at': None, 'invalid_at': None}, {'relation_type': 'HAS_STYLE', 'source_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'target_node_uuid': '95066726921c4e5883a86d8095cd7e0a', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) has a Runner style\", 'valid_at': None, 'invalid_at': None}, {'relation_type': 'MADE_OF', 'source_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'target_node_uuid': 'b9fb205d2511491b83061c432b3f9bf2', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is made of Cotton\", 'valid_at': None, 'invalid_at': None}] in 8471.126079559326 ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: PRODUCED_BY from (UUID: ed9688ba1e9940ff87d3e26bcf5d7ae4) to (UUID: 01ec048c30444e84b0e74a9bed35033d)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: IS_A from (UUID: ed9688ba1e9940ff87d3e26bcf5d7ae4) to (UUID: 77f8b23b74014a7f85fffa0067dbf815)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: HAS_STYLE from (UUID: ed9688ba1e9940ff87d3e26bcf5d7ae4) to (UUID: 95066726921c4e5883a86d8095cd7e0a)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: MADE_OF from (UUID: ed9688ba1e9940ff87d3e26bcf5d7ae4) to (UUID: b9fb205d2511491b83061c432b3f9bf2)\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded The Anytime No Show Sock - Rugged Beige belongs to the Socks category in 0.390362024307251 ms\n", "graphiti_core.nodes - INFO - embedded Manybirds in 0.39443421363830566 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded SuperLight Foam in 0.4058501720428467 ms\n", "graphiti_core.edges - INFO - embedded The Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) is a specific variant of the SuperLight Wool Runner line in 0.4059770107269287 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Cotton in 0.4223036766052246 ms\n", "graphiti_core.nodes - INFO - embedded Shoes in 0.4242551326751709 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded The Women's Tree Breezers Knit - Rugged Beige is a specific variant of the Tree Breezer line in 0.4265608787536621 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Tree Breezer in 0.4428689479827881 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Natural Black in 0.4518458843231201 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Anytime No Show Sock - Rugged Beige in 0.45920896530151367 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Socks in 0.47335124015808105 ms\n", "graphiti_core.edges - INFO - embedded Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is made of Cotton in 0.4767439365386963 ms\n", "graphiti_core.edges - INFO - embedded TinyBirds Wool Runners feature a Blizzard Sole in 0.4791889190673828 ms\n", "graphiti_core.nodes - INFO - embedded Women's Tree Breezers Knit - Rugged Beige in 0.4814419746398926 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) in 0.5008559226989746 ms\n", "graphiti_core.edges - INFO - embedded The Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) are produced by Manybirds in 0.4990081787109375 ms\n", "graphiti_core.edges - INFO - embedded The Men's SuperLight Wool Runners use SuperLight Foam technology for a barely-there feel in 0.5060760974884033 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded TinyBirds Wool Runners are available in Natural Black color in 0.5107131004333496 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Manybirds in 0.5292248725891113 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded SuperLight Wool Runner in 0.5346128940582275 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Manybirds in 0.5513181686401367 ms\n", "graphiti_core.edges - INFO - embedded Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is a type of Shoes in 0.5493569374084473 ms\n", "graphiti_core.nodes - INFO - embedded Manybirds in 0.5559391975402832 ms\n", "graphiti_core.nodes - INFO - embedded Runner in 0.5550639629364014 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) has a Runner style in 0.5574448108673096 ms\n", "graphiti_core.edges - INFO - embedded TinyBirds Wool Runners are manufactured by Manybirds in 0.5622200965881348 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) in 0.5773909091949463 ms\n", "graphiti_core.edges - INFO - embedded Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is produced by Manybirds in 0.5755298137664795 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Manybirds in 0.59409499168396 ms\n", "graphiti_core.edges - INFO - embedded The Anytime No Show Sock - Rugged Beige is manufactured by Manybirds in 0.592015266418457 ms\n", "graphiti_core.nodes - INFO - embedded TinyBirds Wool Runners in 0.6138041019439697 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Blizzard Sole in 0.7478840351104736 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded The Women's Tree Breezers Knit - Rugged Beige is a product made by Manybirds in 0.8393781185150146 ms\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Deduplicated nodes: [{'names': ['Cotton']}, {'names': ['Natural Black']}, {'names': ['SuperLight Foam']}, {'names': ['Shoes']}, {'names': ['Runner']}, {'names': ['Tree Breezer', \"Women's Tree Breezers Knit - Rugged Beige\"]}, {'names': ['Blizzard Sole']}, {'names': ['Socks']}, {'names': [\"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole)\"]}, {'names': ['Anytime No Show Sock - Rugged Beige']}, {'names': ['Manybirds']}, {'names': [\"Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole)\", 'SuperLight Wool Runner']}, {'names': ['TinyBirds Wool Runners']}] in 3240.841865539551 ms\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Deduplicated nodes: [{'names': ['Blizzard Sole']}, {'names': ['Manybirds']}, {'names': ['Runner']}, {'names': ['Tree Breezer']}, {'names': [\"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole)\"]}, {'names': ['SuperLight Foam']}, {'names': [\"Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole)\"]}, {'names': ['TinyBirds Wool Runners']}, {'names': ['Shoes']}, {'names': ['Natural Black']}, {'names': ['Anytime No Show Sock - Rugged Beige']}, {'names': ['Socks']}, {'names': ['<PERSON>']}] in 2772.447109222412 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant nodes: set() in 57.69085884094238 ms\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Deduplicated nodes: [] in 788.3470058441162 ms\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 0b63349f5a3342f1a87be29f316300f1\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 95066726921c4e5883a86d8095cd7e0a\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: e4cadcacd02f42e4b620721dba42bc9a\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 8169219a1c564a53a7201bf215bd45f8\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 138a288fc46f40a18623ccf970d49813\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 0553a72ef65e41999d20a0ffee0b4880\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: b9fb205d2511491b83061c432b3f9bf2\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 24c2e745740c4ba8bc75e60f51cf2865\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: ed9688ba1e9940ff87d3e26bcf5d7ae4\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 7d49a3b6bb4249f7a1262fbfbe6386b0\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 0e96a1b72fe145a79ec2b36842ac6fd9\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 29db0ed04db44b0da0316b277e170aed\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 77f8b23b74014a7f85fffa0067dbf815\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 1c8e93ea8c744cde914e90a8187ba5ba\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 3f217cdd8d3c414d9646ec11cf635e2b\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 348fea3470c64e5986357d6c377b42e5\n", "graphiti_core.edges - INFO - Saved edge to neo4j: c8600c5c591541bc98b08f1316c24bc2\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 369e200c4d554a26a2dd11f545ff3330\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 102bb6a3009f46d8958e543c218e3137\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 7562d31090644f288e24975d69793e1b\n", "graphiti_core.edges - INFO - Saved edge to neo4j: a1c1b3b71c7e4b1ab1472e3a66449af5\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 7994fa049511413eab7c7639a5745142\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 005e267b106a4d40ba8a9dfb62a2b103\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 53c3403f754245a288cce155270c865a\n", "graphiti_core.edges - INFO - Saved edge to neo4j: a389d1435e684a76ba26ffd318a4054b\n", "graphiti_core.edges - INFO - Saved edge to neo4j: c1c947b21d954f8a8bddf7176cde9051\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 24bcd188291e4920a7967dbdb2848b5a\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 8be568a1e9ab4815a444dfad8d4f892a\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 1dd6973059e44f3986731f9d965ddc0a\n", "graphiti_core.edges - INFO - Saved edge to neo4j: d584627fe102459f8e921101a3e3e162\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 052b780c9f3d4bd9b3afb022135f4110\n", "graphiti_core.edges - INFO - Saved edge to neo4j: eff63bd211004e5c922bd90233b7f7e8\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted edge duplicates: [{'uuid': 'f6300668591242d3a64d94bf9de7d4bc', 'fact': 'The Anytime No Show Sock - Rugged Beige belongs to the Socks category'}, {'uuid': 'dfd5aa618d624a8d9a7197192bc3bfa1', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is a type of Shoes\"}, {'uuid': '49866ce679e0455db55116bd540e4e1d', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is made of Cotton\"}, {'uuid': 'cb41175fcb694c3e871881451f5bee78', 'fact': \"The Women's Tree Breezers Knit - Rugged Beige is a specific variant of the Tree Breezer line\"}, {'uuid': '941c96b8d086467fa1cbe6b0f6481604', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) has a Runner style\"}, {'uuid': 'd0f1a94a3df1497096f7dd421cf04a61', 'fact': \"The Men's SuperLight Wool Runners use SuperLight Foam technology for a barely-there feel\"}, {'uuid': '0c150ca1debc423eb7e3bd535413c782', 'fact': \"The Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) is a specific variant of the SuperLight Wool Runner line\"}, {'uuid': 'a4b0fe48994f4b5fa6b4f053a12f83f7', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is produced by Manybirds\"}, {'uuid': '7a22186241414c0a9481f058c99e7c89', 'fact': 'TinyBirds Wool Runners feature a Blizzard Sole'}, {'uuid': 'ea2b6d05e37640408aa5b228496376f5', 'fact': 'TinyBirds Wool Runners are available in Natural Black color'}] in 6294.************ ms \n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted edge duplicates: [{'uuid': 'd0f1a94a3df1497096f7dd421cf04a61', 'fact': \"The Men's SuperLight Wool Runners use SuperLight Foam technology for a barely-there feel\"}, {'uuid': 'a4b0fe48994f4b5fa6b4f053a12f83f7', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is produced by Manybirds\"}, {'uuid': '941c96b8d086467fa1cbe6b0f6481604', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) has a Runner style\"}, {'uuid': '7a22186241414c0a9481f058c99e7c89', 'fact': 'TinyBirds Wool Runners feature a Blizzard Sole'}, {'uuid': '49866ce679e0455db55116bd540e4e1d', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is made of Cotton\"}, {'uuid': 'dfd5aa618d624a8d9a7197192bc3bfa1', 'fact': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is a type of Shoes\"}, {'uuid': '0c150ca1debc423eb7e3bd535413c782', 'fact': \"The Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) is a specific variant of the SuperLight Wool Runner line\"}, {'uuid': 'ea2b6d05e37640408aa5b228496376f5', 'fact': 'TinyBirds Wool Runners are available in Natural Black color'}, {'uuid': 'cb41175fcb694c3e871881451f5bee78', 'fact': \"The Women's Tree Breezers Knit - Rugged Beige is a specific variant of the Tree Breezer line\"}, {'uuid': 'f6300668591242d3a64d94bf9de7d4bc', 'fact': 'The Anytime No Show Sock - Rugged Beige belongs to the Socks category'}] in 5529.************ ms \n", "graphiti_core.search.search_utils - INFO - Found relevant edges: set() in 45.15719413757324 ms\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted unique edges: [{'uuid': 'd0f1a94a3df1497096f7dd421cf04a61'}, {'uuid': 'a4b0fe48994f4b5fa6b4f053a12f83f7'}, {'uuid': '941c96b8d086467fa1cbe6b0f6481604'}, {'uuid': '7a22186241414c0a9481f058c99e7c89'}, {'uuid': '49866ce679e0455db55116bd540e4e1d'}, {'uuid': 'dfd5aa618d624a8d9a7197192bc3bfa1'}, {'uuid': '0c150ca1debc423eb7e3bd535413c782'}, {'uuid': 'ea2b6d05e37640408aa5b228496376f5'}, {'uuid': 'cb41175fcb694c3e871881451f5bee78'}, {'uuid': 'f6300668591242d3a64d94bf9de7d4bc'}]\n", "graphiti_core.graphiti - INFO - extracted edge length: 10\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 49866ce679e0455db55116bd540e4e1d\n", "graphiti_core.edges - INFO - Saved edge to neo4j: d0f1a94a3df1497096f7dd421cf04a61\n", "graphiti_core.edges - INFO - Saved edge to neo4j: ea2b6d05e37640408aa5b228496376f5\n", "graphiti_core.edges - INFO - Saved edge to neo4j: cb41175fcb694c3e871881451f5bee78\n", "graphiti_core.edges - INFO - Saved edge to neo4j: f6300668591242d3a64d94bf9de7d4bc\n", "graphiti_core.edges - INFO - Saved edge to neo4j: a4b0fe48994f4b5fa6b4f053a12f83f7\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 0c150ca1debc423eb7e3bd535413c782\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 7a22186241414c0a9481f058c99e7c89\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 941c96b8d086467fa1cbe6b0f6481604\n", "graphiti_core.edges - INFO - Saved edge to neo4j: dfd5aa618d624a8d9a7197192bc3bfa1\n", "graphiti_core.graphiti - INFO - Completed add_episode_bulk in 37286.25202178955 ms\n"]}], "source": ["await clear_data(client.driver)\n", "await client.build_indices_and_constraints()\n", "await ingest_products_data(client)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': 'AI Assistant', 'labels': ['Entity', 'Speaker'], 'summary': 'AI providing information about product availability'}, {'name': 'Tinybirds Wool Runners', 'labels': ['Entity', 'Product'], 'summary': \"Children's eco-friendly sneakers made with ZQ Merino Wool\"}] in 2495.************ ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: AI Assistant (UUID: a06d832a07fc403f8e43df6b2b650f1a)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Tinybirds Wool Runners (UUID: d3238edc2de14a23bf63b4e0ff751d8c)\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('AI Assistant', 'a06d832a07fc403f8e43df6b2b650f1a'), ('Tinybirds Wool Runners', 'd3238edc2de14a23bf63b4e0ff751d8c')]\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Tinybirds Wool Runners in 0.23474717140197754 ms\n", "graphiti_core.nodes - INFO - embedded AI Assistant in 0.23682188987731934 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant nodes: {'95066726921c4e5883a86d8095cd7e0a', '0553a72ef65e41999d20a0ffee0b4880', '138a288fc46f40a18623ccf970d49813', '24c2e745740c4ba8bc75e60f51cf2865', 'e4cadcacd02f42e4b620721dba42bc9a', '29db0ed04db44b0da0316b277e170aed', '0b63349f5a3342f1a87be29f316300f1', '0e96a1b72fe145a79ec2b36842ac6fd9', '8169219a1c564a53a7201bf215bd45f8', '7d49a3b6bb4249f7a1262fbfbe6386b0', 'ed9688ba1e9940ff87d3e26bcf5d7ae4'} in 7.370948791503906 ms\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('AI Assistant', 'a06d832a07fc403f8e43df6b2b650f1a'), ('Tinybirds Wool Runners', 'd3238edc2de14a23bf63b4e0ff751d8c')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Deduplicated nodes: [{'name': 'Tinybirds Wool Runners', 'duplicate_of': 'TinyBirds Wool Runners'}] in 1036.194086074829 ms\n", "graphiti_core.graphiti - INFO - Adjusted touched nodes: [('AI Assistant', 'a06d832a07fc403f8e43df6b2b650f1a'), ('TinyBirds Wool Runners', '138a288fc46f40a18623ccf970d49813')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'PROVIDES_AVAILABILITY_INFO', 'source_node_uuid': 'a06d832a07fc403f8e43df6b2b650f1a', 'target_node_uuid': '138a288fc46f40a18623ccf970d49813', 'fact': 'AI Assistant informs that all TinyBirds Wool Runners styles are out of stock until December 25th 2024', 'valid_at': None, 'invalid_at': '2024-12-25T00:00:00Z'}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'PROVIDES_AVAILABILITY_INFO', 'source_node_uuid': 'a06d832a07fc403f8e43df6b2b650f1a', 'target_node_uuid': '138a288fc46f40a18623ccf970d49813', 'fact': 'AI Assistant informs that all TinyBirds Wool Runners styles are out of stock until December 25th 2024', 'valid_at': None, 'invalid_at': '2024-12-25T00:00:00Z'}] in 3558.22491645813 ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: PROVIDES_AVAILABILITY_INFO from (UUID: a06d832a07fc403f8e43df6b2b650f1a) to (UUID: 138a288fc46f40a18623ccf970d49813)\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded AI Assistant informs that all TinyBirds Wool Runners styles are out of stock until December 25th 2024 in 0.14994215965270996 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant edges: {'ea2b6d05e37640408aa5b228496376f5', '0c150ca1debc423eb7e3bd535413c782', '7a22186241414c0a9481f058c99e7c89'} in 10.331869125366211 ms\n", "graphiti_core.graphiti - INFO - Existing edges: [('HAS_COLOR_VARIANT', 'ea2b6d05e37640408aa5b228496376f5'), ('HAS_SOLE_TYPE', '7a22186241414c0a9481f058c99e7c89'), ('IS_VARIANT_OF', '0c150ca1debc423eb7e3bd535413c782')]\n", "graphiti_core.graphiti - INFO - Extracted edges: [('PROVIDES_AVAILABILITY_INFO', '150fce971e43402582df51d83e09dddf')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted unique edges: [{'uuid': '150fce971e43402582df51d83e09dddf'}]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The fact states that TinyBirds Wool Runners styles are out of stock until December 25th 2024. This implies that the current unavailability will end on that date, so it is set as the invalid_at date. There is no explicit information about when this unavailability started, so valid_at is left as null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'TinyBirds Wool Runners are available in Natural Black color' does not contain any specific temporal information about when this relationship was established or changed. The current episode mentioning stock availability until December 25th 2024 is not directly related to the color variant relationship, so it is not considered for dating this edge.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'TinyBirds Wool Runners feature a Blizzard Sole' does not contain any temporal information about when this relationship was established or changed. The fact appears to be a general statement about the product's features without any specific dates mentioned.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any specific temporal information about when the Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) became a variant of the SuperLight Wool Runner line. The fact describes an existing relationship without mentioning when it was established or changed.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.graphiti - INFO - Invalidated edges: []\n", "graphiti_core.graphiti - INFO - Edge touched nodes: [('AI Assistant', 'a06d832a07fc403f8e43df6b2b650f1a'), ('TinyBirds Wool Runners', '138a288fc46f40a18623ccf970d49813')]\n", "graphiti_core.graphiti - INFO - Deduped edges: [('PROVIDES_AVAILABILITY_INFO', '150fce971e43402582df51d83e09dddf')]\n", "graphiti_core.graphiti - INFO - Built episodic edges: [EpisodicEdge(uuid='073b5673dcf84c2e8ea1efab526b5b23', source_node_uuid='1de5e192b93149b5a11ede5667d99a40', target_node_uuid='a06d832a07fc403f8e43df6b2b650f1a', created_at=datetime.datetime(2024, 8, 31, 11, 34, 4, 664180)), EpisodicEdge(uuid='6eb49fdd32614291b33d4f93b3e3c2f6', source_node_uuid='1de5e192b93149b5a11ede5667d99a40', target_node_uuid='138a288fc46f40a18623ccf970d49813', created_at=datetime.datetime(2024, 8, 31, 11, 34, 4, 664180))]\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 1de5e192b93149b5a11ede5667d99a40\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 138a288fc46f40a18623ccf970d49813\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: a06d832a07fc403f8e43df6b2b650f1a\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 073b5673dcf84c2e8ea1efab526b5b23\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 6eb49fdd32614291b33d4f93b3e3c2f6\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 0c150ca1debc423eb7e3bd535413c782\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 7a22186241414c0a9481f058c99e7c89\n", "graphiti_core.edges - INFO - Saved edge to neo4j: ea2b6d05e37640408aa5b228496376f5\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 150fce971e43402582df51d83e09dddf\n", "graphiti_core.graphiti - INFO - Completed add_episode in 21647.078037261963 ms\n"]}], "source": ["await client.add_episode(\n", "    name='Inventory management 0',\n", "    episode_body=('All Tinybirds Wool Runners styles are out of stock until December 25th 2024'),\n", "    source=EpisodeType.text,\n", "    reference_time=datetime.now(timezone.utc),\n", "    source_description='Inventory Management Bot',\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.search.search - INFO - search returned context for query Which products are out of stock? in 206.62617683410645 ms\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'150fce971e43402582df51d83e09dddf'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'a06d832a07fc403f8e43df6b2b650f1a'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'138a288fc46f40a18623ccf970d49813'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">34</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">12</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9589</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'PROVIDES_AVAILABILITY_INFO'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'AI Assistant informs that all TinyBirds Wool Runners styles are out of stock until December 25th 2024'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'1de5e192b93149b5a11ede5667d99a40'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">34</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">16</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">47041</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">12</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #808000; text-decoration-color: #808000\">tzinfo</span>=<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">UTC</span><span style=\"font-weight: bold\">&gt;)</span>\n", "<span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'150fce971e43402582df51d83e09dddf'\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'a06d832a07fc403f8e43df6b2b650f1a'\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'138a288fc46f40a18623ccf970d49813'\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m34\u001b[0m, \u001b[1;36m12\u001b[0m, \u001b[1;36m9589\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'PROVIDES_AVAILABILITY_INFO'\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m'AI Assistant informs that all TinyBirds Wool Runners styles are out of stock until December 25th 2024'\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'1de5e192b93149b5a11ede5667d99a40'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m34\u001b[0m, \u001b[1;36m16\u001b[0m, \u001b[1;36m47041\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m12\u001b[0m, \u001b[1;36m25\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[33mtzinfo\u001b[0m=\u001b[1m<\u001b[0m\u001b[1;95mUTC\u001b[0m\u001b[1m>\u001b[0m\u001b[1m)\u001b[0m\n", "\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["r = await client.search('Which products are out of stock?')\n", "\n", "pretty_print(r[0])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': 'SalesBot', 'labels': ['Entity', 'Speaker', 'AI'], 'summary': 'AI assistant for ManyBirds, designed to help customers'}, {'name': 'ManyBirds', 'labels': ['Entity', 'Company'], 'summary': 'Company that the SalesBot represents and assists customers for'}] in 2248.044967651367 ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: SalesBot (UUID: d362076a1e584227bcf51239914e39ad)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: ManyBirds (UUID: cf011889a3ab400aa6d4efa2a5bbf70b)\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('SalesBot', 'd362076a1e584227bcf51239914e39ad'), ('ManyBirds', 'cf011889a3ab400aa6d4efa2a5bbf70b')]\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded SalesBot in 0.15169095993041992 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded ManyBirds in 0.16037321090698242 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant nodes: {'95066726921c4e5883a86d8095cd7e0a', '0553a72ef65e41999d20a0ffee0b4880', '138a288fc46f40a18623ccf970d49813', '24c2e745740c4ba8bc75e60f51cf2865', '29db0ed04db44b0da0316b277e170aed', 'e4cadcacd02f42e4b620721dba42bc9a', '0b63349f5a3342f1a87be29f316300f1', 'a06d832a07fc403f8e43df6b2b650f1a', '77f8b23b74014a7f85fffa0067dbf815', '7d49a3b6bb4249f7a1262fbfbe6386b0', 'ed9688ba1e9940ff87d3e26bcf5d7ae4'} in 6.1740875244140625 ms\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('SalesBot', 'd362076a1e584227bcf51239914e39ad'), ('ManyBirds', 'cf011889a3ab400aa6d4efa2a5bbf70b')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Deduplicated nodes: [{'name': 'ManyBirds', 'duplicate_of': 'Manybirds'}] in 1116.8158054351807 ms\n", "graphiti_core.graphiti - INFO - Adjusted touched nodes: [('SalesBot', 'd362076a1e584227bcf51239914e39ad'), ('Manybirds', '0553a72ef65e41999d20a0ffee0b4880')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'WORKS_FOR', 'source_node_uuid': 'd362076a1e584227bcf51239914e39ad', 'target_node_uuid': '0553a72ef65e41999d20a0ffee0b4880', 'fact': 'SalesB<PERSON> is an AI assistant designed to help customers of ManyBirds', 'valid_at': '2024-07-30T00:00:00Z', 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'WORKS_FOR', 'source_node_uuid': 'd362076a1e584227bcf51239914e39ad', 'target_node_uuid': '0553a72ef65e41999d20a0ffee0b4880', 'fact': '<PERSON><PERSON><PERSON> is an AI assistant designed to help customers of ManyBirds', 'valid_at': '2024-07-30T00:00:00Z', 'invalid_at': None}] in 3275.0120162963867 ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: WORKS_FOR from (UUID: d362076a1e584227bcf51239914e39ad) to (UUID: 0553a72ef65e41999d20a0ffee0b4880)\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded SalesBot is an AI assistant designed to help customers of ManyBirds in 0.21788692474365234 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant edges: {'ea2b6d05e37640408aa5b228496376f5', '150fce971e43402582df51d83e09dddf', 'f6300668591242d3a64d94bf9de7d4bc', 'a4b0fe48994f4b5fa6b4f053a12f83f7'} in 10.164976119995117 ms\n", "graphiti_core.graphiti - INFO - Existing edges: [('PROVIDES_AVAILABILITY_INFO', '150fce971e43402582df51d83e09dddf'), ('HAS_COLOR_VARIANT', 'ea2b6d05e37640408aa5b228496376f5'), ('PRODUCED_BY', 'a4b0fe48994f4b5fa6b4f053a12f83f7'), ('BELONGS_TO_CATEGORY', 'f6300668591242d3a64d94bf9de7d4bc')]\n", "graphiti_core.graphiti - INFO - Extracted edges: [('WORKS_FOR', '1a824bf8d9a54f47ba6cbb9265239c28')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted unique edges: [{'uuid': '1a824bf8d9a54f47ba6cbb9265239c28'}]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any specific temporal information about when SalesBot started or stopped working for ManyBirds. The fact simply states that SalesBot is an AI assistant designed to help ManyBirds customers, without mentioning when this relationship was established or if it has changed.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The fact states that TinyBirds Wool Runners styles are out of stock until December 25th 2024. This implies that the availability information is valid up to this date, so it is set as the invalid_at date. The valid_at is null because there's no information about when this unavailability started.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any temporal information about when the color variant relationship was established or changed. It simply states that TinyBirds Wool Runners are available in Natural Black color, without specifying when this became true or if it will change in the future.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any temporal information about when the production relationship between Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) and Manybirds was established or changed. The fact simply states that the product is produced by Manybirds without specifying any dates.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any temporal information about when the relationship between 'The Anytime No Show Sock - Rugged Beige' and the 'Socks' category was established or changed. The fact simply states a current categorization without mentioning any specific dates or times.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.graphiti - INFO - Invalidated edges: []\n", "graphiti_core.graphiti - INFO - Edge touched nodes: [('SalesBot', 'd362076a1e584227bcf51239914e39ad'), ('Manybirds', '0553a72ef65e41999d20a0ffee0b4880')]\n", "graphiti_core.graphiti - INFO - Deduped edges: [('WORKS_FOR', '1a824bf8d9a54f47ba6cbb9265239c28')]\n", "graphiti_core.graphiti - INFO - Built episodic edges: [EpisodicEdge(uuid='37e26764259f477d8989433c653ca608', source_node_uuid='b71ff21bdc3e4bc89493e8ce54192605', target_node_uuid='d362076a1e584227bcf51239914e39ad', created_at=datetime.datetime(2024, 8, 31, 11, 34, 26, 572499)), EpisodicEdge(uuid='33eed830fe0e40bebd8a3788ef955626', source_node_uuid='b71ff21bdc3e4bc89493e8ce54192605', target_node_uuid='0553a72ef65e41999d20a0ffee0b4880', created_at=datetime.datetime(2024, 8, 31, 11, 34, 26, 572499))]\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: b71ff21bdc3e4bc89493e8ce54192605\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 0553a72ef65e41999d20a0ffee0b4880\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: d362076a1e584227bcf51239914e39ad\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 37e26764259f477d8989433c653ca608\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 33eed830fe0e40bebd8a3788ef955626\n", "graphiti_core.edges - INFO - Saved edge to neo4j: ea2b6d05e37640408aa5b228496376f5\n", "graphiti_core.edges - INFO - Saved edge to neo4j: a4b0fe48994f4b5fa6b4f053a12f83f7\n", "graphiti_core.edges - INFO - Saved edge to neo4j: f6300668591242d3a64d94bf9de7d4bc\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 150fce971e43402582df51d83e09dddf\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 1a824bf8d9a54f47ba6cbb9265239c28\n", "graphiti_core.graphiti - INFO - Completed add_episode in 24251.09887123108 ms\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': '<PERSON>', 'labels': ['Entity', 'Speaker', 'Customer'], 'summary': 'Customer looking for a new pair of shoes'}, {'name': 'Shoes', 'labels': ['Entity', 'Product'], 'summary': 'Footwear product that <PERSON> is interested in purchasing'}] in 2049.052953720093 ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: <PERSON> (UUID: c4091c3ffc814f2c9017304361898585)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Shoes (UUID: 1146d707f6924135a68e180a4ed8cdc5)\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('<PERSON>', 'c4091c3ffc814f2c9017304361898585'), ('Shoes', '1146d707f6924135a68e180a4ed8cdc5')]\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded John in 0.1756269931793213 ms\n", "graphiti_core.nodes - INFO - embedded Shoes in 0.17654705047607422 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant nodes: {'95066726921c4e5883a86d8095cd7e0a', '77f8b23b74014a7f85fffa0067dbf815', '24c2e745740c4ba8bc75e60f51cf2865', '8169219a1c564a53a7201bf215bd45f8', '29db0ed04db44b0da0316b277e170aed', '0e96a1b72fe145a79ec2b36842ac6fd9', '0b63349f5a3342f1a87be29f316300f1', 'b9fb205d2511491b83061c432b3f9bf2', '7d49a3b6bb4249f7a1262fbfbe6386b0', 'ed9688ba1e9940ff87d3e26bcf5d7ae4'} in 5.251884460449219 ms\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('<PERSON>', 'c4091c3ffc814f2c9017304361898585'), ('Shoes', '1146d707f6924135a68e180a4ed8cdc5')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Deduplicated nodes: [{'name': 'Shoes', 'duplicate_of': 'Shoes'}] in 1559.2992305755615 ms\n", "graphiti_core.graphiti - INFO - Adjusted touched nodes: [('<PERSON>', 'c4091c3ffc814f2c9017304361898585'), ('Shoes', '77f8b23b74014a7f85fffa0067dbf815')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'INTERESTED_IN', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': '77f8b23b74014a7f85fffa0067dbf815', 'fact': '<PERSON> is looking for a new pair of shoes', 'valid_at': '2024-07-30T00:01:00Z', 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'INTERESTED_IN', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': '77f8b23b74014a7f85fffa0067dbf815', 'fact': '<PERSON> is looking for a new pair of shoes', 'valid_at': '2024-07-30T00:01:00Z', 'invalid_at': None}] in 2793.************ ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: INTERESTED_IN from (UUID: c4091c3ffc814f2c9017304361898585) to (UUID: 77f8b23b74014a7f85fffa0067dbf815)\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded <PERSON> is looking for a new pair of shoes in 0.15775108337402344 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant edges: {'a4b0fe48994f4b5fa6b4f053a12f83f7', 'd0f1a94a3df1497096f7dd421cf04a61', '941c96b8d086467fa1cbe6b0f6481604', 'dfd5aa618d624a8d9a7197192bc3bfa1', 'cb41175fcb694c3e871881451f5bee78'} in 8.713006973266602 ms\n", "graphiti_core.graphiti - INFO - Existing edges: [('IS_A', 'dfd5aa618d624a8d9a7197192bc3bfa1'), ('HAS_STYLE', '941c96b8d086467fa1cbe6b0f6481604'), ('PRODUCED_BY', 'a4b0fe48994f4b5fa6b4f053a12f83f7'), ('USES_TECHNOLOGY', 'd0f1a94a3df1497096f7dd421cf04a61'), ('IS_VARIANT_OF', 'cb41175fcb694c3e871881451f5bee78')]\n", "graphiti_core.graphiti - INFO - Extracted edges: [('INTERESTED_IN', '2a9cf189e19649c19ec127c4024cfe51')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted unique edges: [{'uuid': '2a9cf189e19649c19ec127c4024cfe51'}]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the timestamp of the current episode where <PERSON> expresses interest in looking for a new pair of shoes. There is no information about when this interest might end, so invalid_at is null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is a type of Shoes' is a general classification statement. There are no specific dates mentioned in the fact that indicate when this relationship was established or changed. The fact appears to be a constant truth about the product category, not tied to any particular time frame.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) has a Runner style' does not contain any temporal information about when this relationship was established or changed. The fact appears to be a static attribute of the product. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the 'created_at' timestamp of the product, which indicates when the product was first added to the system and thus when the production relationship was established. There is no information about when or if this relationship ended, so invalid_at is set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any specific temporal information about when the Men's SuperLight Wool Runners started or stopped using SuperLight Foam technology. The fact simply states that the product uses this technology, without mentioning when this relationship was established or if it has changed over time.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any temporal information about when the Women's Tree Breezers Knit - Rugged Beige became a variant of the Tree Breezer line. The fact simply states a current relationship without specifying when it was established or if it has changed over time.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.graphiti - INFO - Invalidated edges: []\n", "graphiti_core.graphiti - INFO - Edge touched nodes: [('<PERSON>', 'c4091c3ffc814f2c9017304361898585'), ('Shoes', '77f8b23b74014a7f85fffa0067dbf815')]\n", "graphiti_core.graphiti - INFO - Deduped edges: [('INTERESTED_IN', '2a9cf189e19649c19ec127c4024cfe51')]\n", "graphiti_core.graphiti - INFO - Built episodic edges: [EpisodicEdge(uuid='f31ead808d7048bbacb1094927ab149f', source_node_uuid='c2ebc79d2a204efb845be84b6dbf69d7', target_node_uuid='c4091c3ffc814f2c9017304361898585', created_at=datetime.datetime(2024, 8, 31, 11, 34, 50, 818298)), EpisodicEdge(uuid='e4794ef2280f4e0891a700a8c2b68f8b', source_node_uuid='c2ebc79d2a204efb845be84b6dbf69d7', target_node_uuid='77f8b23b74014a7f85fffa0067dbf815', created_at=datetime.datetime(2024, 8, 31, 11, 34, 50, 818298))]\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: c2ebc79d2a204efb845be84b6dbf69d7\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: c4091c3ffc814f2c9017304361898585\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 77f8b23b74014a7f85fffa0067dbf815\n", "graphiti_core.edges - INFO - Saved edge to neo4j: f31ead808d7048bbacb1094927ab149f\n", "graphiti_core.edges - INFO - Saved edge to neo4j: e4794ef2280f4e0891a700a8c2b68f8b\n", "graphiti_core.edges - INFO - Saved edge to neo4j: dfd5aa618d624a8d9a7197192bc3bfa1\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 941c96b8d086467fa1cbe6b0f6481604\n", "graphiti_core.edges - INFO - Saved edge to neo4j: d0f1a94a3df1497096f7dd421cf04a61\n", "graphiti_core.edges - INFO - Saved edge to neo4j: cb41175fcb694c3e871881451f5bee78\n", "graphiti_core.edges - INFO - Saved edge to neo4j: a4b0fe48994f4b5fa6b4f053a12f83f7\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 2a9cf189e19649c19ec127c4024cfe51\n", "graphiti_core.graphiti - INFO - Completed add_episode in 23286.057949066162 ms\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': 'SalesBot', 'labels': ['Entity', 'Speaker', 'AI'], 'summary': 'AI assistant helping with shoe selection'}, {'name': 'Shoes', 'labels': ['Entity', 'Product'], 'summary': 'Footwear being discussed in the conversation'}, {'name': 'Material', 'labels': ['Entity', 'Attribute'], 'summary': 'Characteristic of shoes being inquired about'}] in 2447.7028846740723 ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: SalesBot (UUID: 0f8d7fdee46e4ea584139cce9759aba9)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Shoes (UUID: ed0921355b5e4d068ac07692cd2d7fe2)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Material (UUID: c4efdae7ab9240fd8b8f59ac741a19bf)\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('SalesBot', '0f8d7fdee46e4ea584139cce9759aba9'), ('Shoes', 'ed0921355b5e4d068ac07692cd2d7fe2'), ('Material', 'c4efdae7ab9240fd8b8f59ac741a19bf')]\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Shoes in 0.17450499534606934 ms\n", "graphiti_core.nodes - INFO - embedded Material in 0.17970609664916992 ms\n", "graphiti_core.nodes - INFO - embedded SalesBot in 0.19498395919799805 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant nodes: {'c4091c3ffc814f2c9017304361898585', '95066726921c4e5883a86d8095cd7e0a', '77f8b23b74014a7f85fffa0067dbf815', '8169219a1c564a53a7201bf215bd45f8', '29db0ed04db44b0da0316b277e170aed', 'a06d832a07fc403f8e43df6b2b650f1a', '0e96a1b72fe145a79ec2b36842ac6fd9', '0b63349f5a3342f1a87be29f316300f1', '24c2e745740c4ba8bc75e60f51cf2865', 'e4cadcacd02f42e4b620721dba42bc9a', 'd362076a1e584227bcf51239914e39ad', 'b9fb205d2511491b83061c432b3f9bf2', '7d49a3b6bb4249f7a1262fbfbe6386b0', 'ed9688ba1e9940ff87d3e26bcf5d7ae4'} in 7.69805908203125 ms\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('SalesBot', '0f8d7fdee46e4ea584139cce9759aba9'), ('Shoes', 'ed0921355b5e4d068ac07692cd2d7fe2'), ('Material', 'c4efdae7ab9240fd8b8f59ac741a19bf')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Deduplicated nodes: [{'name': 'SalesBot', 'duplicate_of': 'SalesBot'}, {'name': 'Shoes', 'duplicate_of': 'Shoes'}] in 1357.1619987487793 ms\n", "graphiti_core.graphiti - INFO - Adjusted touched nodes: [('SalesBot', 'd362076a1e584227bcf51239914e39ad'), ('Shoes', '77f8b23b74014a7f85fffa0067dbf815'), ('Material', 'c4efdae7ab9240fd8b8f59ac741a19bf')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'INQUIRES_ABOUT', 'source_node_uuid': 'd362076a1e584227bcf51239914e39ad', 'target_node_uuid': 'c4efdae7ab9240fd8b8f59ac741a19bf', 'fact': 'SalesBot asks about the material of shoes the customer is looking for', 'valid_at': '2024-07-30T00:02:00Z', 'invalid_at': None}, {'relation_type': 'RELATES_TO', 'source_node_uuid': 'c4efdae7ab9240fd8b8f59ac741a19bf', 'target_node_uuid': '77f8b23b74014a7f85fffa0067dbf815', 'fact': 'Material is a characteristic of shoes being inquired about', 'valid_at': '2024-07-30T00:02:00Z', 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'INQUIRES_ABOUT', 'source_node_uuid': 'd362076a1e584227bcf51239914e39ad', 'target_node_uuid': 'c4efdae7ab9240fd8b8f59ac741a19bf', 'fact': 'SalesBot asks about the material of shoes the customer is looking for', 'valid_at': '2024-07-30T00:02:00Z', 'invalid_at': None}, {'relation_type': 'RELATES_TO', 'source_node_uuid': 'c4efdae7ab9240fd8b8f59ac741a19bf', 'target_node_uuid': '77f8b23b74014a7f85fffa0067dbf815', 'fact': 'Material is a characteristic of shoes being inquired about', 'valid_at': '2024-07-30T00:02:00Z', 'invalid_at': None}] in 2947.************ ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: INQUIRES_ABOUT from (UUID: d362076a1e584227bcf51239914e39ad) to (UUID: c4efdae7ab9240fd8b8f59ac741a19bf)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: RELATES_TO from (UUID: c4efdae7ab9240fd8b8f59ac741a19bf) to (UUID: 77f8b23b74014a7f85fffa0067dbf815)\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded Material is a characteristic of shoes being inquired about in 0.13653302192687988 ms\n", "graphiti_core.edges - INFO - embedded SalesBot asks about the material of shoes the customer is looking for in 0.14820313453674316 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant edges: {'a4b0fe48994f4b5fa6b4f053a12f83f7', 'd0f1a94a3df1497096f7dd421cf04a61', '2a9cf189e19649c19ec127c4024cfe51', 'dfd5aa618d624a8d9a7197192bc3bfa1', 'cb41175fcb694c3e871881451f5bee78', '1a824bf8d9a54f47ba6cbb9265239c28'} in 25.244712829589844 ms\n", "graphiti_core.graphiti - INFO - Existing edges: [('INTERESTED_IN', '2a9cf189e19649c19ec127c4024cfe51'), ('IS_A', 'dfd5aa618d624a8d9a7197192bc3bfa1'), ('WORKS_FOR', '1a824bf8d9a54f47ba6cbb9265239c28'), ('PRODUCED_BY', 'a4b0fe48994f4b5fa6b4f053a12f83f7'), ('USES_TECHNOLOGY', 'd0f1a94a3df1497096f7dd421cf04a61'), ('IS_VARIANT_OF', 'cb41175fcb694c3e871881451f5bee78')]\n", "graphiti_core.graphiti - INFO - Extracted edges: [('INQUIRES_ABOUT', '1086271667484ba2aa579eaa2d69dab8'), ('RELATES_TO', '3a17fda8f6074cb6878448897703d464')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted unique edges: [{'uuid': '1086271667484ba2aa579eaa2d69dab8'}, {'uuid': '3a17fda8f6074cb6878448897703d464'}]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the timestamp of the current episode where <PERSON>B<PERSON> asks about the material of shoes, which establishes the INQUIRES_ABOUT relationship. There is no information provided about when this inquiry ends, so invalid_at is set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Material is a characteristic of shoes being inquired about' does not contain any specific temporal information about when this relationship was established or changed. The conversation does not provide any dates directly related to when material became a characteristic of shoes being inquired about. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the timestamp when <PERSON> expressed interest in looking for a new pair of shoes. The invalid_at is null because there's no information about when this interest might end.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is a type of Shoes' does not contain any temporal information about when this relationship was established or changed. The fact appears to be a general classification statement without any specific time reference.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The provided edge fact does not contain any specific temporal information about when SalesBot started or stopped working for ManyBirds. The fact only states that SalesBot is an AI assistant designed to help customers of ManyBirds, but it does not mention when this relationship was established or if it has changed. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any temporal information about when the production relationship between Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) and Manybirds was established or changed. The conversation and provided context also do not offer any relevant dates for this specific relationship. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any temporal information about when the Men's SuperLight Wool Runners started or stopped using SuperLight Foam technology. The fact simply states that the shoes use this technology, without specifying when this relationship began or ended. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any temporal information about when the Women's Tree Breezers Knit - Rugged Beige variant was established or when it might have ceased to be a variant of the Tree Breezer line. The fact simply states a current relationship without any reference to time.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.graphiti - INFO - Invalidated edges: []\n", "graphiti_core.graphiti - INFO - Edge touched nodes: [('SalesBot', 'd362076a1e584227bcf51239914e39ad'), ('Shoes', '77f8b23b74014a7f85fffa0067dbf815'), ('Material', 'c4efdae7ab9240fd8b8f59ac741a19bf')]\n", "graphiti_core.graphiti - INFO - Deduped edges: [('INQUIRES_ABOUT', '1086271667484ba2aa579eaa2d69dab8'), ('RELATES_TO', '3a17fda8f6074cb6878448897703d464')]\n", "graphiti_core.graphiti - INFO - Built episodic edges: [EpisodicEdge(uuid='9728567c4ce944a690967bf3ac8ffa9a', source_node_uuid='aa28834a26ea406c9082aa71f25fa638', target_node_uuid='d362076a1e584227bcf51239914e39ad', created_at=datetime.datetime(2024, 8, 31, 11, 35, 14, 104998)), EpisodicEdge(uuid='0faf6989f7454fe889e1e6b5e836f871', source_node_uuid='aa28834a26ea406c9082aa71f25fa638', target_node_uuid='77f8b23b74014a7f85fffa0067dbf815', created_at=datetime.datetime(2024, 8, 31, 11, 35, 14, 104998)), EpisodicEdge(uuid='b3f2c603873148fcb6db2969c5a15993', source_node_uuid='aa28834a26ea406c9082aa71f25fa638', target_node_uuid='c4efdae7ab9240fd8b8f59ac741a19bf', created_at=datetime.datetime(2024, 8, 31, 11, 35, 14, 104998))]\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: aa28834a26ea406c9082aa71f25fa638\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 77f8b23b74014a7f85fffa0067dbf815\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: d362076a1e584227bcf51239914e39ad\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: c4efdae7ab9240fd8b8f59ac741a19bf\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 0faf6989f7454fe889e1e6b5e836f871\n", "graphiti_core.edges - INFO - Saved edge to neo4j: b3f2c603873148fcb6db2969c5a15993\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 9728567c4ce944a690967bf3ac8ffa9a\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 2a9cf189e19649c19ec127c4024cfe51\n", "graphiti_core.edges - INFO - Saved edge to neo4j: dfd5aa618d624a8d9a7197192bc3bfa1\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 1a824bf8d9a54f47ba6cbb9265239c28\n", "graphiti_core.edges - INFO - Saved edge to neo4j: d0f1a94a3df1497096f7dd421cf04a61\n", "graphiti_core.edges - INFO - Saved edge to neo4j: cb41175fcb694c3e871881451f5bee78\n", "graphiti_core.edges - INFO - Saved edge to neo4j: a4b0fe48994f4b5fa6b4f053a12f83f7\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 1086271667484ba2aa579eaa2d69dab8\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 3a17fda8f6074cb6878448897703d464\n", "graphiti_core.graphiti - INFO - Completed add_episode in 24882.755279541016 ms\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': '<PERSON>', 'labels': ['Entity', 'Speaker', 'Customer'], 'summary': 'The customer looking for new shoes'}, {'name': 'Wool', 'labels': ['Entity', 'Material'], 'summary': 'A material <PERSON> is allergic to'}, {'name': 'Size 10', 'labels': ['Entity', 'ShoeSize'], 'summary': \"<PERSON>'s shoe size\"}] in 1825.1228332519531 ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: <PERSON> (UUID: ee93a09830ea45a9ae8629595bdb0977)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Wool (UUID: ccd7590b3601440f9ae816507da79130)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Size 10 (UUID: fcea4a4539244cd28aac1bb11def0cab)\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('<PERSON>', 'ee93a09830ea45a9ae8629595bdb0977'), ('Wool', 'ccd7590b3601440f9ae816507da79130'), ('Size 10', 'fcea4a4539244cd28aac1bb11def0cab')]\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded John in 0.1800851821899414 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Size 10 in 0.21727991104125977 ms\n", "graphiti_core.nodes - INFO - embedded Wool in 0.24567413330078125 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant nodes: {'c4091c3ffc814f2c9017304361898585', '95066726921c4e5883a86d8095cd7e0a', '0553a72ef65e41999d20a0ffee0b4880', '138a288fc46f40a18623ccf970d49813', '8169219a1c564a53a7201bf215bd45f8', 'e4cadcacd02f42e4b620721dba42bc9a', '29db0ed04db44b0da0316b277e170aed', '0b63349f5a3342f1a87be29f316300f1', '0e96a1b72fe145a79ec2b36842ac6fd9', '24c2e745740c4ba8bc75e60f51cf2865', 'c4efdae7ab9240fd8b8f59ac741a19bf', 'd362076a1e584227bcf51239914e39ad', 'b9fb205d2511491b83061c432b3f9bf2', 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'a06d832a07fc403f8e43df6b2b650f1a'} in 7.748126983642578 ms\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('<PERSON>', 'ee93a09830ea45a9ae8629595bdb0977'), ('Wool', 'ccd7590b3601440f9ae816507da79130'), ('Size 10', 'fcea4a4539244cd28aac1bb11def0cab')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Deduplicated nodes: [{'name': '<PERSON>', 'duplicate_of': '<PERSON>'}] in 1051.************ ms\n", "graphiti_core.graphiti - INFO - Adjusted touched nodes: [('<PERSON>', 'c4091c3ffc814f2c9017304361898585'), ('Wool', 'ccd7590b3601440f9ae816507da79130'), ('Size 10', 'fcea4a4539244cd28aac1bb11def0cab')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'IS_ALLERGIC_TO', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': 'ccd7590b3601440f9ae816507da79130', 'fact': '<PERSON> is allergic to wool', 'valid_at': '2024-07-30T00:03:00Z', 'invalid_at': None}, {'relation_type': 'HAS_SHOE_SIZE', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': 'fcea4a4539244cd28aac1bb11def0cab', 'fact': \"<PERSON>'s shoe size is 10\", 'valid_at': '2024-07-30T00:03:00Z', 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'IS_ALLERGIC_TO', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': 'ccd7590b3601440f9ae816507da79130', 'fact': '<PERSON> is allergic to wool', 'valid_at': '2024-07-30T00:03:00Z', 'invalid_at': None}, {'relation_type': 'HAS_SHOE_SIZE', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': 'fcea4a4539244cd28aac1bb11def0cab', 'fact': \"<PERSON>'s shoe size is 10\", 'valid_at': '2024-07-30T00:03:00Z', 'invalid_at': None}] in 2610.9251976013184 ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: IS_ALLERGIC_TO from (UUID: c4091c3ffc814f2c9017304361898585) to (UUID: ccd7590b3601440f9ae816507da79130)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: HAS_SHOE_SIZE from (UUID: c4091c3ffc814f2c9017304361898585) to (UUID: fcea4a4539244cd28aac1bb11def0cab)\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded <PERSON> is allergic to wool in 0.12508010864257812 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded <PERSON>'s shoe size is 10 in 0.1933460235595703 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant edges: {'150fce971e43402582df51d83e09dddf', '3a17fda8f6074cb6878448897703d464', '2a9cf189e19649c19ec127c4024cfe51', 'f6300668591242d3a64d94bf9de7d4bc', '7a22186241414c0a9481f058c99e7c89', 'dfd5aa618d624a8d9a7197192bc3bfa1', '1a824bf8d9a54f47ba6cbb9265239c28'} in 13.681173324584961 ms\n", "graphiti_core.graphiti - INFO - Existing edges: [('INTERESTED_IN', '2a9cf189e19649c19ec127c4024cfe51'), ('HAS_SOLE_TYPE', '7a22186241414c0a9481f058c99e7c89'), ('PROVIDES_AVAILABILITY_INFO', '150fce971e43402582df51d83e09dddf'), ('IS_A', 'dfd5aa618d624a8d9a7197192bc3bfa1'), ('RELATES_TO', '3a17fda8f6074cb6878448897703d464'), ('WORKS_FOR', '1a824bf8d9a54f47ba6cbb9265239c28'), ('BELONGS_TO_CATEGORY', 'f6300668591242d3a64d94bf9de7d4bc')]\n", "graphiti_core.graphiti - INFO - Extracted edges: [('IS_ALLERGIC_TO', 'e4cd07dfddc84072985aa8cf4e1dc01b'), ('HAS_SHOE_SIZE', '6a19ae37d5074d808d4f951ab347e2b1')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted unique edges: [{'uuid': 'e4cd07dfddc84072985aa8cf4e1dc01b'}, {'uuid': '6a19ae37d5074d808d4f951ab347e2b1'}]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the timestamp of the current episode where <PERSON> states he is allergic to wool. There is no information about when this allergy might end, so invalid_at is null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the timestamp of the current episode where <PERSON> mentions his shoe size. There is no information about when this fact might become invalid, so invalid_at is set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the timestamp when <PERSON> first expressed interest in looking for a new pair of shoes. The invalid_at is null because there's no information about when this interest might end.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'TinyBirds Wool Runners feature a Blizzard Sole' does not contain any temporal information about when this relationship was established or changed. The conversation and provided context also do not offer any relevant dates for this specific product feature. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact states that TinyBirds Wool Runners styles are out of stock until December 25th 2024. This implies that the availability information is valid up to this date, so it is set as the invalid_at date. The valid_at is null because there's no information about when this availability status began.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is a type of Shoes' does not contain any temporal information about when this relationship was established or changed. The conversation and provided context also do not offer any relevant dates for this specific relationship. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Material is a characteristic of shoes being inquired about' does not contain any specific temporal information about when this relationship was established or changed. The conversation does not provide any dates directly related to this fact. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any temporal information about when SalesBot started or stopped working for ManyBirds. The fact only states that SalesBot is an AI assistant designed to help ManyBirds customers, without specifying when this relationship began or if it has ended.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any temporal information about when the relationship between 'The Anytime No Show Sock - Rugged Beige' and the 'Socks' category was established or changed. The fact simply states a categorical relationship without any reference to time.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.graphiti - INFO - Invalidated edges: []\n", "graphiti_core.graphiti - INFO - Edge touched nodes: [('<PERSON>', 'c4091c3ffc814f2c9017304361898585'), ('Wool', 'ccd7590b3601440f9ae816507da79130'), ('Size 10', 'fcea4a4539244cd28aac1bb11def0cab')]\n", "graphiti_core.graphiti - INFO - Deduped edges: [('IS_ALLERGIC_TO', 'e4cd07dfddc84072985aa8cf4e1dc01b'), ('HAS_SHOE_SIZE', '6a19ae37d5074d808d4f951ab347e2b1')]\n", "graphiti_core.graphiti - INFO - Built episodic edges: [EpisodicEdge(uuid='eb4c11dbea6546cf8b12c98a25a838de', source_node_uuid='6b41a387ca504a2686b636a20b5673a3', target_node_uuid='c4091c3ffc814f2c9017304361898585', created_at=datetime.datetime(2024, 8, 31, 11, 35, 38, 987280)), EpisodicEdge(uuid='e52c1a7362054fb492450dfd9c7e11f6', source_node_uuid='6b41a387ca504a2686b636a20b5673a3', target_node_uuid='ccd7590b3601440f9ae816507da79130', created_at=datetime.datetime(2024, 8, 31, 11, 35, 38, 987280)), EpisodicEdge(uuid='08db825ce44a46a2a3246c7596823485', source_node_uuid='6b41a387ca504a2686b636a20b5673a3', target_node_uuid='fcea4a4539244cd28aac1bb11def0cab', created_at=datetime.datetime(2024, 8, 31, 11, 35, 38, 987280))]\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 6b41a387ca504a2686b636a20b5673a3\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: c4091c3ffc814f2c9017304361898585\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: ccd7590b3601440f9ae816507da79130\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: fcea4a4539244cd28aac1bb11def0cab\n", "graphiti_core.edges - INFO - Saved edge to neo4j: e52c1a7362054fb492450dfd9c7e11f6\n", "graphiti_core.edges - INFO - Saved edge to neo4j: eb4c11dbea6546cf8b12c98a25a838de\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 08db825ce44a46a2a3246c7596823485\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 2a9cf189e19649c19ec127c4024cfe51\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 7a22186241414c0a9481f058c99e7c89\n", "graphiti_core.edges - INFO - Saved edge to neo4j: dfd5aa618d624a8d9a7197192bc3bfa1\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 3a17fda8f6074cb6878448897703d464\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 1a824bf8d9a54f47ba6cbb9265239c28\n", "graphiti_core.edges - INFO - Saved edge to neo4j: f6300668591242d3a64d94bf9de7d4bc\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 150fce971e43402582df51d83e09dddf\n", "graphiti_core.edges - INFO - Saved edge to neo4j: e4cd07dfddc84072985aa8cf4e1dc01b\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 6a19ae37d5074d808d4f951ab347e2b1\n", "graphiti_core.graphiti - INFO - Completed add_episode in 24849.345922470093 ms\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': 'SalesBot', 'labels': ['Entity', 'Speaker'], 'summary': 'AI sales assistant helping with shoe selection'}, {'name': \"Men's Couriers\", 'labels': ['Entity', 'Product'], 'summary': 'Shoe model with a retro silhouette look'}, {'name': 'Cotton', 'labels': ['Entity', 'Material'], 'summary': \"Material used in the Men's Couriers shoes\"}, {'name': 'Basin Blue', 'labels': ['Entity', 'Color'], 'summary': \"Color option for the Men's Couriers shoes\"}] in 2770.1427936553955 ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: SalesBot (UUID: 696fce9d66a54b278b2a269c26661b3b)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Men's Couriers (UUID: 3a841033bb0941fdbe030127c68fe6f4)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: <PERSON> (UUID: 8229ecdec24b4731966e943b174c2448)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Basin Blue (UUID: 588989497641456fb33243f035731f98)\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('SalesBot', '696fce9d66a54b278b2a269c26661b3b'), (\"Men's Couriers\", '3a841033bb0941fdbe030127c68fe6f4'), ('Cotton', '8229ecdec24b4731966e943b174c2448'), ('Basin Blue', '588989497641456fb33243f035731f98')]\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Cotton in 0.14429593086242676 ms\n", "graphiti_core.nodes - INFO - embedded Basin Blue in 0.14951014518737793 ms\n", "graphiti_core.nodes - INFO - embedded Men's Couriers in 0.1525580883026123 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded SalesBot in 0.2479569911956787 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant nodes: {'95066726921c4e5883a86d8095cd7e0a', 'ccd7590b3601440f9ae816507da79130', 'fcea4a4539244cd28aac1bb11def0cab', '24c2e745740c4ba8bc75e60f51cf2865', '8169219a1c564a53a7201bf215bd45f8', '29db0ed04db44b0da0316b277e170aed', 'e4cadcacd02f42e4b620721dba42bc9a', '0b63349f5a3342f1a87be29f316300f1', '0e96a1b72fe145a79ec2b36842ac6fd9', 'c4efdae7ab9240fd8b8f59ac741a19bf', 'd362076a1e584227bcf51239914e39ad', 'b9fb205d2511491b83061c432b3f9bf2', '7d49a3b6bb4249f7a1262fbfbe6386b0', 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'a06d832a07fc403f8e43df6b2b650f1a'} in 10.065078735351562 ms\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('SalesBot', '696fce9d66a54b278b2a269c26661b3b'), (\"Men's Couriers\", '3a841033bb0941fdbe030127c68fe6f4'), ('Cotton', '8229ecdec24b4731966e943b174c2448'), ('Basin Blue', '588989497641456fb33243f035731f98')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Deduplicated nodes: [{'name': 'SalesBot', 'duplicate_of': 'SalesBot'}, {'name': \"Men's Couriers\", 'duplicate_of': \"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole)\"}, {'name': '<PERSON>', 'duplicate_of': '<PERSON>'}] in 1589.2488956451416 ms\n", "graphiti_core.graphiti - INFO - Adjusted touched nodes: [('SalesBot', 'd362076a1e584227bcf51239914e39ad'), (\"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole)\", 'ed9688ba1e9940ff87d3e26bcf5d7ae4'), ('Cotton', 'b9fb205d2511491b83061c432b3f9bf2'), ('Basin Blue', '588989497641456fb33243f035731f98')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'RECOMMENDS', 'source_node_uuid': 'd362076a1e584227bcf51239914e39ad', 'target_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'fact': \"SalesBot recommends Men's Couriers shoes to the customer\", 'valid_at': '2024-07-30T00:04:00Z', 'invalid_at': None}, {'relation_type': 'MADE_OF', 'source_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'target_node_uuid': 'b9fb205d2511491b83061c432b3f9bf2', 'fact': \"Men's Couriers shoes are made from cotton\", 'valid_at': '2024-07-30T00:04:00Z', 'invalid_at': None}, {'relation_type': 'HAS_COLOR_OPTION', 'source_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'target_node_uuid': '588989497641456fb33243f035731f98', 'fact': \"Men's Couriers shoes are available in Basin Blue color\", 'valid_at': '2024-07-30T00:04:00Z', 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'RECOMMENDS', 'source_node_uuid': 'd362076a1e584227bcf51239914e39ad', 'target_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'fact': \"SalesBot recommends Men's Couriers shoes to the customer\", 'valid_at': '2024-07-30T00:04:00Z', 'invalid_at': None}, {'relation_type': 'MADE_OF', 'source_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'target_node_uuid': 'b9fb205d2511491b83061c432b3f9bf2', 'fact': \"Men's Couriers shoes are made from cotton\", 'valid_at': '2024-07-30T00:04:00Z', 'invalid_at': None}, {'relation_type': 'HAS_COLOR_OPTION', 'source_node_uuid': 'ed9688ba1e9940ff87d3e26bcf5d7ae4', 'target_node_uuid': '588989497641456fb33243f035731f98', 'fact': \"Men's Couriers shoes are available in Basin Blue color\", 'valid_at': '2024-07-30T00:04:00Z', 'invalid_at': None}] in 4071.************ ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: RECOMMENDS from (UUID: d362076a1e584227bcf51239914e39ad) to (UUID: ed9688ba1e9940ff87d3e26bcf5d7ae4)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: MADE_OF from (UUID: ed9688ba1e9940ff87d3e26bcf5d7ae4) to (UUID: b9fb205d2511491b83061c432b3f9bf2)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: HAS_COLOR_OPTION from (UUID: ed9688ba1e9940ff87d3e26bcf5d7ae4) to (UUID: 588989497641456fb33243f035731f98)\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded Men's Couriers shoes are made from cotton in 0.1536571979522705 ms\n", "graphiti_core.edges - INFO - embedded SalesBot recommends Men's Couriers shoes to the customer in 0.15691208839416504 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded Men's Couriers shoes are available in Basin Blue color in 0.19091391563415527 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant edges: {'ea2b6d05e37640408aa5b228496376f5', 'a4b0fe48994f4b5fa6b4f053a12f83f7', 'f6300668591242d3a64d94bf9de7d4bc', '941c96b8d086467fa1cbe6b0f6481604', '49866ce679e0455db55116bd540e4e1d', '1086271667484ba2aa579eaa2d69dab8', 'dfd5aa618d624a8d9a7197192bc3bfa1', '1a824bf8d9a54f47ba6cbb9265239c28'} in 47.464847564697266 ms\n", "graphiti_core.graphiti - INFO - Existing edges: [('INQUIRES_ABOUT', '1086271667484ba2aa579eaa2d69dab8'), ('IS_A', 'dfd5aa618d624a8d9a7197192bc3bfa1'), ('HAS_STYLE', '941c96b8d086467fa1cbe6b0f6481604'), ('MADE_OF', '49866ce679e0455db55116bd540e4e1d'), ('PRODUCED_BY', 'a4b0fe48994f4b5fa6b4f053a12f83f7'), ('WORKS_FOR', '1a824bf8d9a54f47ba6cbb9265239c28'), ('BELONGS_TO_CATEGORY', 'f6300668591242d3a64d94bf9de7d4bc'), ('HAS_COLOR_VARIANT', 'ea2b6d05e37640408aa5b228496376f5')]\n", "graphiti_core.graphiti - INFO - Extracted edges: [('RECOMMENDS', '4721330c8f2b45e69e07f520773f8794'), ('MADE_OF', 'd7579abf2a164c5aa6af2e0d76d15f82'), ('HAS_COLOR_OPTION', 'eb443cba70e145e2ba6f65d49b465ded')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted unique edges: [{'uuid': '4721330c8f2b45e69e07f520773f8794'}, {'uuid': 'eb443cba70e145e2ba6f65d49b465ded'}]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the timestamp of the current episode where SalesBot recommends the Men's Couriers shoes. The invalid_at is null because there's no information about when this recommendation ends or changes.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any specific temporal information about when the color option became available or when it might cease to be available. The fact simply states that Men's Couriers shoes are available in Basin Blue color, without mentioning any dates or times related to this availability.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The INQUIRES_ABOUT relationship was established when SalesBot asked about the material of shoes the customer is looking for. This occurred in the second episode of the conversation at 2024-07-30T00:02:00Z. There is no information about when this relationship ended, so invalid_at is set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is a type of Shoes' does not contain any temporal information about when this relationship was established or changed. The conversation does not provide any dates related to the creation or modification of this classification. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) has a Runner style' does not contain any temporal information about when this style relationship was established or changed. The conversation and provided timestamps do not directly relate to the formation or alteration of this product's style attribute. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is made of Cotton' does not contain any temporal information about when this relationship was established or changed. The conversation does not provide any dates specifically related to when the shoes were made of cotton. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is produced by Manybirds' does not contain any temporal information about when this production relationship was established or changed. The conversation and provided timestamps do not offer any relevant dates for the production of this specific shoe model by Manybirds. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The provided edge fact and conversation do not contain any specific temporal information about when SalesBot started or stopped working for ManyBirds. The fact only states that SalesBot is an AI assistant designed to help customers of ManyBirds, but does not provide any dates for the establishment or change of this relationship.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any temporal information about when the relationship between 'The Anytime No Show Sock - Rugged Beige' and the 'Socks' category was established or changed. The fact simply states a categorical relationship without any reference to time.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'TinyBirds Wool Runners are available in Natural Black color' does not contain any temporal information about when this color variant became available or when it might cease to be available. The conversation does not provide any additional information about the timing of this specific product's color availability. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.graphiti - INFO - Invalidated edges: []\n", "graphiti_core.graphiti - INFO - Edge touched nodes: [('SalesBot', 'd362076a1e584227bcf51239914e39ad'), (\"Men's Couriers - Natural Black/Basin Blue (Blizzard Sole)\", 'ed9688ba1e9940ff87d3e26bcf5d7ae4'), ('Basin Blue', '588989497641456fb33243f035731f98')]\n", "graphiti_core.graphiti - INFO - Deduped edges: [('RECOMMENDS', '4721330c8f2b45e69e07f520773f8794'), ('HAS_COLOR_OPTION', 'eb443cba70e145e2ba6f65d49b465ded')]\n", "graphiti_core.graphiti - INFO - Built episodic edges: [EpisodicEdge(uuid='181be6289ee24e7a8e9abae89770af91', source_node_uuid='e7c29d5d38854cac801bc07d236240a8', target_node_uuid='d362076a1e584227bcf51239914e39ad', created_at=datetime.datetime(2024, 8, 31, 11, 36, 3, 837016)), EpisodicEdge(uuid='591c09b62eb74aae9c69327c2dac9de9', source_node_uuid='e7c29d5d38854cac801bc07d236240a8', target_node_uuid='ed9688ba1e9940ff87d3e26bcf5d7ae4', created_at=datetime.datetime(2024, 8, 31, 11, 36, 3, 837016)), EpisodicEdge(uuid='cd6672352dd4451cbebb13df36d8b635', source_node_uuid='e7c29d5d38854cac801bc07d236240a8', target_node_uuid='588989497641456fb33243f035731f98', created_at=datetime.datetime(2024, 8, 31, 11, 36, 3, 837016))]\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: e7c29d5d38854cac801bc07d236240a8\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: d362076a1e584227bcf51239914e39ad\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: ed9688ba1e9940ff87d3e26bcf5d7ae4\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: b9fb205d2511491b83061c432b3f9bf2\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 588989497641456fb33243f035731f98\n", "graphiti_core.edges - INFO - Saved edge to neo4j: cd6672352dd4451cbebb13df36d8b635\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 181be6289ee24e7a8e9abae89770af91\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 591c09b62eb74aae9c69327c2dac9de9\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 1086271667484ba2aa579eaa2d69dab8\n", "graphiti_core.edges - INFO - Saved edge to neo4j: dfd5aa618d624a8d9a7197192bc3bfa1\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 941c96b8d086467fa1cbe6b0f6481604\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 49866ce679e0455db55116bd540e4e1d\n", "graphiti_core.edges - INFO - Saved edge to neo4j: a4b0fe48994f4b5fa6b4f053a12f83f7\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 1a824bf8d9a54f47ba6cbb9265239c28\n", "graphiti_core.edges - INFO - Saved edge to neo4j: f6300668591242d3a64d94bf9de7d4bc\n", "graphiti_core.edges - INFO - Saved edge to neo4j: ea2b6d05e37640408aa5b228496376f5\n", "graphiti_core.edges - INFO - Saved edge to neo4j: eb443cba70e145e2ba6f65d49b465ded\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 4721330c8f2b45e69e07f520773f8794\n", "graphiti_core.graphiti - INFO - Completed add_episode in 31496.28973007202 ms\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': '<PERSON>', 'labels': ['Entity', 'Speaker', 'Customer'], 'summary': 'The customer making the purchase decision'}, {'name': \"Men's Couriers\", 'labels': ['Entity', 'Product'], 'summary': 'The shoes <PERSON> is purchasing'}, {'name': 'Basin Blue', 'labels': ['Entity', 'Color'], 'summary': \"The color of the Men's Couriers shoes <PERSON> is buying\"}] in 1983.1140041351318 ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: <PERSON> (UUID: 8167b66b5ff644089794b9128790042c)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Men's Couriers (UUID: b30e3ba27aa14f88895156331a435237)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Basin Blue (UUID: b1be7390af7548aab5913c50703d0be1)\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('<PERSON>', '8167b66b5ff644089794b9128790042c'), (\"Men's Couriers\", 'b30e3ba27aa14f88895156331a435237'), ('Basin Blue', 'b1be7390af7548aab5913c50703d0be1')]\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Basin Blue in 0.15884017944335938 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded John in 0.19483017921447754 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Men's Couriers in 0.41947317123413086 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant nodes: {'c4091c3ffc814f2c9017304361898585', '95066726921c4e5883a86d8095cd7e0a', 'ccd7590b3601440f9ae816507da79130', 'fcea4a4539244cd28aac1bb11def0cab', '8169219a1c564a53a7201bf215bd45f8', '24c2e745740c4ba8bc75e60f51cf2865', 'e4cadcacd02f42e4b620721dba42bc9a', '29db0ed04db44b0da0316b277e170aed', '0b63349f5a3342f1a87be29f316300f1', '0e96a1b72fe145a79ec2b36842ac6fd9', '588989497641456fb33243f035731f98', 'c4efdae7ab9240fd8b8f59ac741a19bf', '7d49a3b6bb4249f7a1262fbfbe6386b0', 'ed9688ba1e9940ff87d3e26bcf5d7ae4'} in 12.174844741821289 ms\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('<PERSON>', '8167b66b5ff644089794b9128790042c'), (\"Men's Couriers\", 'b30e3ba27aa14f88895156331a435237'), ('Basin Blue', 'b1be7390af7548aab5913c50703d0be1')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Deduplicated nodes: [{'name': '<PERSON>', 'duplicate_of': '<PERSON>'}, {'name': 'Basin Blue', 'duplicate_of': 'Basin Blue'}] in 1147.1989154815674 ms\n", "graphiti_core.graphiti - INFO - Adjusted touched nodes: [('<PERSON>', 'c4091c3ffc814f2c9017304361898585'), (\"Men's Couriers\", 'b30e3ba27aa14f88895156331a435237'), ('Basin Blue', '588989497641456fb33243f035731f98')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'PURCHASES', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': 'b30e3ba27aa14f88895156331a435237', 'fact': \"<PERSON> decides to purchase the Men's Couriers shoes\", 'valid_at': '2024-07-30T00:05:00Z', 'invalid_at': None}, {'relation_type': 'HAS_COLOR', 'source_node_uuid': 'b30e3ba27aa14f88895156331a435237', 'target_node_uuid': '588989497641456fb33243f035731f98', 'fact': \"The Men's Couriers shoes <PERSON> is purchasing are in Basin Blue color\", 'valid_at': '2024-07-30T00:05:00Z', 'invalid_at': None}, {'relation_type': 'LIKES', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': '588989497641456fb33243f035731f98', 'fact': '<PERSON> expresses that he likes the Basin Blue color for the shoes', 'valid_at': '2024-07-30T00:05:00Z', 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'PURCHASES', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': 'b30e3ba27aa14f88895156331a435237', 'fact': \"<PERSON> decides to purchase the Men's Couriers shoes\", 'valid_at': '2024-07-30T00:05:00Z', 'invalid_at': None}, {'relation_type': 'HAS_COLOR', 'source_node_uuid': 'b30e3ba27aa14f88895156331a435237', 'target_node_uuid': '588989497641456fb33243f035731f98', 'fact': \"The Men's Couriers shoes <PERSON> is purchasing are in Basin Blue color\", 'valid_at': '2024-07-30T00:05:00Z', 'invalid_at': None}, {'relation_type': 'LIKES', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': '588989497641456fb33243f035731f98', 'fact': '<PERSON> expresses that he likes the Basin Blue color for the shoes', 'valid_at': '2024-07-30T00:05:00Z', 'invalid_at': None}] in 3899.3918895721436 ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: PURCHASES from (UUID: c4091c3ffc814f2c9017304361898585) to (UUID: b30e3ba27aa14f88895156331a435237)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: HAS_COLOR from (UUID: b30e3ba27aa14f88895156331a435237) to (UUID: 588989497641456fb33243f035731f98)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: LIKES from (UUID: c4091c3ffc814f2c9017304361898585) to (UUID: 588989497641456fb33243f035731f98)\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded <PERSON> decides to purchase the Men's Couriers shoes in 0.1658470630645752 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded <PERSON> expresses that he likes the Basin Blue color for the shoes in 0.19078302383422852 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded The Men's Couriers shoes John is purchasing are in Basin Blue color in 0.756566047668457 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant edges: {'ea2b6d05e37640408aa5b228496376f5', 'a4b0fe48994f4b5fa6b4f053a12f83f7', '2a9cf189e19649c19ec127c4024cfe51', '4721330c8f2b45e69e07f520773f8794', 'f6300668591242d3a64d94bf9de7d4bc', 'e4cd07dfddc84072985aa8cf4e1dc01b', 'eb443cba70e145e2ba6f65d49b465ded', '1086271667484ba2aa579eaa2d69dab8', '6a19ae37d5074d808d4f951ab347e2b1', 'dfd5aa618d624a8d9a7197192bc3bfa1'} in 21.873950958251953 ms\n", "graphiti_core.graphiti - INFO - Existing edges: [('INTERESTED_IN', '2a9cf189e19649c19ec127c4024cfe51'), ('RECOMMENDS', '4721330c8f2b45e69e07f520773f8794'), ('HAS_SHOE_SIZE', '6a19ae37d5074d808d4f951ab347e2b1'), ('HAS_COLOR_OPTION', 'eb443cba70e145e2ba6f65d49b465ded'), ('IS_A', 'dfd5aa618d624a8d9a7197192bc3bfa1'), ('PRODUCED_BY', 'a4b0fe48994f4b5fa6b4f053a12f83f7'), ('IS_ALLERGIC_TO', 'e4cd07dfddc84072985aa8cf4e1dc01b'), ('BELONGS_TO_CATEGORY', 'f6300668591242d3a64d94bf9de7d4bc'), ('HAS_COLOR_VARIANT', 'ea2b6d05e37640408aa5b228496376f5'), ('INQUIRES_ABOUT', '1086271667484ba2aa579eaa2d69dab8')]\n", "graphiti_core.graphiti - INFO - Extracted edges: [('PURCHASES', '199ec767d52c47d2a5965f3197b1c4d2'), ('HAS_COLOR', '9b2867f902734f35b4e2ce1011f039e8'), ('LIKES', 'df1d2e82a40e40e1b3734c2298774a6b')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted unique edges: [{'uuid': '199ec767d52c47d2a5965f3197b1c4d2'}, {'uuid': 'df1d2e82a40e40e1b3734c2298774a6b'}]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to 2024-07-30T00:05:00Z because this is the timestamp of the current episode where <PERSON> decides to purchase the Men's Couriers shoes. The invalid_at is set to null as there is no information about when this purchase relationship ends.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the timestamp of <PERSON>'s message where he expresses his liking for the Basin Blue color. The invalid_at is null as there's no information about when this preference might end.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact '<PERSON> is looking for a new pair of shoes' does not contain any specific temporal information about when this interest began or ended. The conversation provides context about <PERSON>'s shoe shopping experience, but it doesn't establish when <PERSON> started looking for shoes or when this interest might end. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The SalesBot recommends Men's Couriers shoes to the customer in the message sent at 2024-07-30T00:04:00Z. This is when the RECOMMENDS relationship is established. There is no information about when this recommendation ends or becomes invalid, so invalid_at is set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to 2024-07-30T00:03:00Z because <PERSON> explicitly states his shoe size in the conversation at that timestamp. There is no information about when this fact might become invalid, so invalid_at is set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Men's Couriers shoes are available in Basin Blue color' does not contain any specific temporal information about when this color option became available or when it might cease to be available. The conversation provides no additional dates related to the establishment or change of this color option. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is a type of Shoes' does not contain any temporal information about when this relationship was established or changed. The conversation mentions the product but does not provide any dates related to its classification as a type of shoes. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) is produced by Manybirds' does not contain any temporal information about when this production relationship was established or ended. The conversation does not provide any dates related to the production of the shoes by Manybirds. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact '<PERSON> is allergic to wool' does not contain any specific temporal information about when this allergy began or ended. The conversation mentions <PERSON>'s allergy, but it doesn't provide any dates or times related to the establishment or change of this allergic condition. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'The Anytime No Show Sock - Rugged Beige belongs to the Socks category' does not contain any temporal information about when this categorization was established or changed. The conversation and provided timestamps do not relate to the formation or alteration of this product category relationship. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'TinyBirds Wool Runners are available in Natural Black color' does not contain any temporal information about when this color variant became available or when it might cease to be available. The conversation does not provide any additional information about the establishment or change of this specific color variant relationship. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the timestamp when SalesBot asked about the material of shoes, which is directly related to the INQUIRES_ABOUT edge. There is no information provided about when this inquiry ended or became invalid, so invalid_at is set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.graphiti - INFO - Invalidated edges: []\n", "graphiti_core.graphiti - INFO - Edge touched nodes: [('<PERSON>', 'c4091c3ffc814f2c9017304361898585'), (\"Men's Couriers\", 'b30e3ba27aa14f88895156331a435237'), ('Basin Blue', '588989497641456fb33243f035731f98')]\n", "graphiti_core.graphiti - INFO - Deduped edges: [('PURCHASES', '199ec767d52c47d2a5965f3197b1c4d2'), ('LIKES', 'df1d2e82a40e40e1b3734c2298774a6b')]\n", "graphiti_core.graphiti - INFO - Built episodic edges: [EpisodicEdge(uuid='f7ecaffc0e49489cabac3ed648d3c700', source_node_uuid='4c8afb4aa1b446899a85249df475bc66', target_node_uuid='c4091c3ffc814f2c9017304361898585', created_at=datetime.datetime(2024, 8, 31, 11, 36, 35, 332675)), EpisodicEdge(uuid='0595ecd84b4b43608e4013bef5d6b1b6', source_node_uuid='4c8afb4aa1b446899a85249df475bc66', target_node_uuid='b30e3ba27aa14f88895156331a435237', created_at=datetime.datetime(2024, 8, 31, 11, 36, 35, 332675)), EpisodicEdge(uuid='eaa3184ea1c9413b80ce63af78b02ba9', source_node_uuid='4c8afb4aa1b446899a85249df475bc66', target_node_uuid='588989497641456fb33243f035731f98', created_at=datetime.datetime(2024, 8, 31, 11, 36, 35, 332675))]\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 4c8afb4aa1b446899a85249df475bc66\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: c4091c3ffc814f2c9017304361898585\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: b30e3ba27aa14f88895156331a435237\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 588989497641456fb33243f035731f98\n", "graphiti_core.edges - INFO - Saved edge to neo4j: f7ecaffc0e49489cabac3ed648d3c700\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 0595ecd84b4b43608e4013bef5d6b1b6\n", "graphiti_core.edges - INFO - Saved edge to neo4j: eaa3184ea1c9413b80ce63af78b02ba9\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 4721330c8f2b45e69e07f520773f8794\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 6a19ae37d5074d808d4f951ab347e2b1\n", "graphiti_core.edges - INFO - Saved edge to neo4j: dfd5aa618d624a8d9a7197192bc3bfa1\n", "graphiti_core.edges - INFO - Saved edge to neo4j: eb443cba70e145e2ba6f65d49b465ded\n", "graphiti_core.edges - INFO - Saved edge to neo4j: a4b0fe48994f4b5fa6b4f053a12f83f7\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 1086271667484ba2aa579eaa2d69dab8\n", "graphiti_core.edges - INFO - Saved edge to neo4j: f6300668591242d3a64d94bf9de7d4bc\n", "graphiti_core.edges - INFO - Saved edge to neo4j: ea2b6d05e37640408aa5b228496376f5\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 2a9cf189e19649c19ec127c4024cfe51\n", "graphiti_core.edges - INFO - Saved edge to neo4j: e4cd07dfddc84072985aa8cf4e1dc01b\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 199ec767d52c47d2a5965f3197b1c4d2\n", "graphiti_core.edges - INFO - Saved edge to neo4j: df1d2e82a40e40e1b3734c2298774a6b\n", "graphiti_core.graphiti - INFO - Completed add_episode in 34139.6062374115 ms\n"]}], "source": ["await add_messages(client, shoe_conversation_1, prefix='conversation-1')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.search.search - INFO - search returned context for query What is <PERSON>'s shoe size? in 204.0848731994629 ms\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'6a19ae37d5074d808d4f951ab347e2b1'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'fcea4a4539244cd28aac1bb11def0cab'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">35</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">44</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">738829</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'HAS_SHOE_SIZE'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">\"<PERSON>'s shoe size is 10\"</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'6b41a387ca504a2686b636a20b5673a3'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>, <span style=\"color: #808000; text-decoration-color: #808000\">tzinfo</span>=<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">UTC</span><span style=\"font-weight: bold\">&gt;)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">}</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'0c150ca1debc423eb7e3bd535413c782'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'0e96a1b72fe145a79ec2b36842ac6fd9'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'0e96a1b72fe145a79ec2b36842ac6fd9'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">33</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">39</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">424173</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'IS_VARIANT_OF'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">\"The Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) is a specific variant of the SuperLight Wool Runner line\"</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'4a302ac072c94f9da876535b1130e03d'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'6a19ae37d5074d808d4f951ab347e2b1'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'fcea4a4539244cd28aac1bb11def0cab'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m35\u001b[0m, \u001b[1;36m44\u001b[0m, \u001b[1;36m738829\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'HAS_SHOE_SIZE'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m\"<PERSON>'s shoe size is 10\"\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'6b41a387ca504a2686b636a20b5673a3'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m7\u001b[0m, \u001b[1;36m30\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m3\u001b[0m, \u001b[33mtzinfo\u001b[0m=\u001b[1m<\u001b[0m\u001b[1;95mUTC\u001b[0m\u001b[1m>\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m}\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'0c150ca1debc423eb7e3bd535413c782'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'0e96a1b72fe145a79ec2b36842ac6fd9'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'0e96a1b72fe145a79ec2b36842ac6fd9'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m33\u001b[0m, \u001b[1;36m39\u001b[0m, \u001b[1;36m424173\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'IS_VARIANT_OF'\u001b[0m,\n", "\u001b[2;32m<PERSON>   <PERSON>   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m\"The Men's SuperLight Wool Runners - Dark Grey \u001b[0m\u001b[32m(\u001b[0m\u001b[32mMedium Grey Sole\u001b[0m\u001b[32m)\u001b[0m\u001b[32m is a specific variant of the SuperLight Wool Runner line\"\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'4a302ac072c94f9da876535b1130e03d'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["r = await client.search(\"What is <PERSON>'s shoe size?\", num_results=2)\n", "\n", "pretty_print(r)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.search.search_utils - INFO - Found relevant nodes: {'c4091c3ffc814f2c9017304361898585', '95066726921c4e5883a86d8095cd7e0a', 'ccd7590b3601440f9ae816507da79130', 'fcea4a4539244cd28aac1bb11def0cab', '8169219a1c564a53a7201bf215bd45f8', 'b30e3ba27aa14f88895156331a435237', 'c4efdae7ab9240fd8b8f59ac741a19bf'} in 8.331060409545898 ms\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">EntityNode</span><span style=\"font-weight: bold\">(</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #808000; text-decoration-color: #808000\">uuid</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'John'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #808000; text-decoration-color: #808000\">labels</span>=<span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Entity'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #808000; text-decoration-color: #808000\">created_at</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">34</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">52</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">870658</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #808000; text-decoration-color: #808000\">name_embedding</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #808000; text-decoration-color: #808000\">summary</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'Customer looking for a new pair of shoes'</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mEntityNode\u001b[0m\u001b[1m(\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[33muuid\u001b[0m=\u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[33mname\u001b[0m=\u001b[32m'<PERSON>'\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[33mlabels\u001b[0m=\u001b[1m[\u001b[0m\u001b[32m'Entity'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[33mcreated_at\u001b[0m=\u001b[1;35mdatetime\u001b[0m\u001b[1;35m.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m34\u001b[0m, \u001b[1;36m52\u001b[0m, \u001b[1;36m870658\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[33mname_embedding\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[33msummary\u001b[0m=\u001b[32m'Customer looking for a new pair of shoes'\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_RRF\n", "\n", "nl = await client._search('<PERSON>', NODE_HYBRID_SEARCH_RRF)\n", "\n", "pretty_print(nl[0])\n", "\n", "john_uuid = nl[0].uuid"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.search.search - INFO - search returned context for query Can <PERSON> wear ManyBirds Wool Runners? in 252.65789031982422 ms\n", "----------------------------------------------------------------------------------------------------\n", "Standard Reciprocal Rank Fusion Reranking\n", "----------------------------------------------------------------------------------------------------\n", "TinyBirds Wool Runners are available in Natural Black color\n", "The Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) is a specific variant of the SuperLight Wool Runner line\n", "<PERSON> is allergic to wool\n"]}], "source": ["r = await client.search('Can <PERSON> wear ManyBirds Wool Runners?', num_results=3)\n", "\n", "print('-' * 100)\n", "print('Standard Reciprocal Rank Fusion Reranking')\n", "print('-' * 100)\n", "for record in r:\n", "    print(record.fact)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.search.search - INFO - search returned context for query Can <PERSON> wear ManyBirds Wool Runners? in 310.61410903930664 ms\n", "----------------------------------------------------------------------------------------------------\n", "Node Distance Reranking from 'John' node\n", "----------------------------------------------------------------------------------------------------\n", "TinyBirds Wool Runners are available in Natural Black color\n", "The Men's SuperLight Wool Runners - Dark Grey (Medium Grey Sole) is a specific variant of the SuperLight Wool Runner line\n", "<PERSON> is allergic to wool\n"]}], "source": ["r = await client.search(\n", "    'Can <PERSON> wear ManyBirds Wool Runners?', center_node_uuid=john_uuid, num_results=3\n", ")\n", "\n", "print('-' * 100)\n", "print(\"Node Distance Reranking from 'John' node\")\n", "print('-' * 100)\n", "for record in r:\n", "    print(record.fact)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': 'SalesBot', 'labels': ['Entity', 'Speaker', 'AI'], 'summary': 'AI sales assistant engaging with the customer'}, {'name': '<PERSON>', 'labels': ['Entity', 'Customer'], 'summary': 'Customer being addressed by the SalesBot'}] in 1890.************ ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: SalesBot (UUID: c807d7ac10014a6faf0c5e4c9dbc3eac)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: <PERSON> (UUID: cbef7be8d9a5481dbe2f56be97d0e462)\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('SalesBot', 'c807d7ac10014a6faf0c5e4c9dbc3eac'), ('<PERSON>', 'cbef7be8d9a5481dbe2f56be97d0e462')]\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded SalesBot in 0.15208911895751953 ms\n", "graphiti_core.nodes - INFO - embedded John in 0.16043972969055176 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant nodes: {'c4091c3ffc814f2c9017304361898585', '95066726921c4e5883a86d8095cd7e0a', 'ccd7590b3601440f9ae816507da79130', 'fcea4a4539244cd28aac1bb11def0cab', '24c2e745740c4ba8bc75e60f51cf2865', '8169219a1c564a53a7201bf215bd45f8', 'b30e3ba27aa14f88895156331a435237', '0b63349f5a3342f1a87be29f316300f1', 'c4efdae7ab9240fd8b8f59ac741a19bf', 'd362076a1e584227bcf51239914e39ad', '7d49a3b6bb4249f7a1262fbfbe6386b0', 'a06d832a07fc403f8e43df6b2b650f1a'} in 12.486934661865234 ms\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('SalesBot', 'c807d7ac10014a6faf0c5e4c9dbc3eac'), ('<PERSON>', 'cbef7be8d9a5481dbe2f56be97d0e462')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Deduplicated nodes: [{'name': 'SalesBot', 'duplicate_of': 'SalesBot'}, {'name': '<PERSON>', 'duplicate_of': '<PERSON>'}] in 1143.9518928527832 ms\n", "graphiti_core.graphiti - INFO - Adjusted touched nodes: [('SalesBot', 'd362076a1e584227bcf51239914e39ad'), ('<PERSON>', 'c4091c3ffc814f2c9017304361898585')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'ASSISTS', 'source_node_uuid': 'd362076a1e584227bcf51239914e39ad', 'target_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'fact': 'SalesBot offers assistance to <PERSON>', 'valid_at': '2024-08-20T00:00:00Z', 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'ASSISTS', 'source_node_uuid': 'd362076a1e584227bcf51239914e39ad', 'target_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'fact': 'SalesBot offers assistance to <PERSON>', 'valid_at': '2024-08-20T00:00:00Z', 'invalid_at': None}] in 1712.4040126800537 ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: ASSISTS from (UUID: d362076a1e584227bcf51239914e39ad) to (UUID: c4091c3ffc814f2c9017304361898585)\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded SalesBot offers assistance to <PERSON> in 0.14788413047790527 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant edges: {'4721330c8f2b45e69e07f520773f8794', '199ec767d52c47d2a5965f3197b1c4d2', 'e4cd07dfddc84072985aa8cf4e1dc01b', '1a824bf8d9a54f47ba6cbb9265239c28'} in 11.628150939941406 ms\n", "graphiti_core.graphiti - INFO - Existing edges: [('WORKS_FOR', '1a824bf8d9a54f47ba6cbb9265239c28'), ('RECOMMENDS', '4721330c8f2b45e69e07f520773f8794'), ('PURCHASES', '199ec767d52c47d2a5965f3197b1c4d2'), ('IS_ALLERGIC_TO', 'e4cd07dfddc84072985aa8cf4e1dc01b')]\n", "graphiti_core.graphiti - INFO - Extracted edges: [('ASSISTS', '518d5ef539004ceca7b9b9a750e22bd4')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted unique edges: [{'uuid': '518d5ef539004ceca7b9b9a750e22bd4'}]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to 2024-08-20T00:00:00Z because the current episode shows SalesBot offering assistance to <PERSON> on this date. The invalid_at is null as there's no information about when this assistance relationship ends.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The provided edge fact and conversation do not contain any specific temporal information about when SalesBot started or stopped working for ManyBirds. The fact only states that SalesBot is designed to help customers of ManyBirds, but does not provide any dates for the establishment or change of this relationship.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The recommendation was made by SalesBot in the previous episode dated 2024-07-30T00:04:00Z. This is when the RECOMMENDS relationship was established. There is no information about when or if this recommendation became invalid, so invalid_at is set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not provide any specific temporal information about when <PERSON> actually purchases the Men's Couriers shoes. It only states that <PERSON> decides to purchase them, but doesn't specify when the purchase occurs. Therefore, no dates can be confidently extracted for the PURCHASES relationship.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the timestamp of <PERSON>'s message where he explicitly states his allergy to wool. There is no information about when this allergy might end, so invalid_at is null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.graphiti - INFO - Invalidated edges: []\n", "graphiti_core.graphiti - INFO - Edge touched nodes: [('SalesBot', 'd362076a1e584227bcf51239914e39ad'), ('<PERSON>', 'c4091c3ffc814f2c9017304361898585')]\n", "graphiti_core.graphiti - INFO - Deduped edges: [('ASSISTS', '518d5ef539004ceca7b9b9a750e22bd4')]\n", "graphiti_core.graphiti - INFO - Built episodic edges: [EpisodicEdge(uuid='90f7a075a6cd4adf940f0ae2c713cb4f', source_node_uuid='7087342bfe86423bb702060fa9cc612b', target_node_uuid='d362076a1e584227bcf51239914e39ad', created_at=datetime.datetime(2024, 8, 31, 11, 37, 10, 490493)), EpisodicEdge(uuid='e06099d0b4014d619ea0fd23b9c034e3', source_node_uuid='7087342bfe86423bb702060fa9cc612b', target_node_uuid='c4091c3ffc814f2c9017304361898585', created_at=datetime.datetime(2024, 8, 31, 11, 37, 10, 490493))]\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 7087342bfe86423bb702060fa9cc612b\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: d362076a1e584227bcf51239914e39ad\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: c4091c3ffc814f2c9017304361898585\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 90f7a075a6cd4adf940f0ae2c713cb4f\n", "graphiti_core.edges - INFO - Saved edge to neo4j: e06099d0b4014d619ea0fd23b9c034e3\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 1a824bf8d9a54f47ba6cbb9265239c28\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 4721330c8f2b45e69e07f520773f8794\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 199ec767d52c47d2a5965f3197b1c4d2\n", "graphiti_core.edges - INFO - Saved edge to neo4j: e4cd07dfddc84072985aa8cf4e1dc01b\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 518d5ef539004ceca7b9b9a750e22bd4\n", "graphiti_core.graphiti - INFO - Completed add_episode in 17025.1567363739 ms\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': '<PERSON>', 'labels': ['Entity', 'Speaker', 'Customer'], 'summary': 'Customer seeking to return a product'}, {'name': \"Men's Couriers\", 'labels': ['Entity', 'Product'], 'summary': 'Shoes purchased by <PERSON> that he wants to return'}, {'name': 'Wide Feet', 'labels': ['Entity', 'Physical Characteristic'], 'summary': \"<PERSON>'s foot type causing discomfort with the shoes\"}] in 5912.************ ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: <PERSON> (UUID: ede531cb06004e13ae2c35a933bc8b3a)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Men's Couriers (UUID: 6425b2af8442458f902986289fa6b758)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Wide Feet (UUID: 8b43988e689b437095c7e75aa1044490)\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('<PERSON>', 'ede531cb06004e13ae2c35a933bc8b3a'), (\"Men's Couriers\", '6425b2af8442458f902986289fa6b758'), ('Wide Feet', '8b43988e689b437095c7e75aa1044490')]\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded John in 0.16251802444458008 ms\n", "graphiti_core.nodes - INFO - embedded Wide Feet in 0.17085790634155273 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Men's Couriers in 0.45365405082702637 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant nodes: {'c4091c3ffc814f2c9017304361898585', '95066726921c4e5883a86d8095cd7e0a', 'ccd7590b3601440f9ae816507da79130', 'fcea4a4539244cd28aac1bb11def0cab', '8169219a1c564a53a7201bf215bd45f8', '29db0ed04db44b0da0316b277e170aed', 'b30e3ba27aa14f88895156331a435237', '0e96a1b72fe145a79ec2b36842ac6fd9', '0b63349f5a3342f1a87be29f316300f1', '588989497641456fb33243f035731f98', 'c4efdae7ab9240fd8b8f59ac741a19bf', '7d49a3b6bb4249f7a1262fbfbe6386b0', 'ed9688ba1e9940ff87d3e26bcf5d7ae4'} in 18.983125686645508 ms\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('<PERSON>', 'ede531cb06004e13ae2c35a933bc8b3a'), (\"Men's Couriers\", '6425b2af8442458f902986289fa6b758'), ('Wide Feet', '8b43988e689b437095c7e75aa1044490')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Deduplicated nodes: [{'name': '<PERSON>', 'duplicate_of': '<PERSON>'}, {'name': \"Men's Couriers\", 'duplicate_of': \"Men's Couriers\"}] in 1266.4299011230469 ms\n", "graphiti_core.graphiti - INFO - Adjusted touched nodes: [('<PERSON>', 'c4091c3ffc814f2c9017304361898585'), (\"Men's Couriers\", 'b30e3ba27aa14f88895156331a435237'), ('Wide Feet', '8b43988e689b437095c7e75aa1044490')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'PURCHASED', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': 'b30e3ba27aa14f88895156331a435237', 'fact': \"<PERSON> bought the Men's Couriers shoes\", 'valid_at': '2024-07-30T00:05:00Z', 'invalid_at': None}, {'relation_type': 'CAUSES_DISCOMFORT', 'source_node_uuid': '8b43988e689b437095c7e75aa1044490', 'target_node_uuid': 'b30e3ba27aa14f88895156331a435237', 'fact': \"<PERSON>'s wide feet cause discomfort with the Men's Couriers shoes\", 'valid_at': '2024-08-20T00:01:00Z', 'invalid_at': None}, {'relation_type': 'HAS_CHARACTERISTIC', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': '8b43988e689b437095c7e75aa1044490', 'fact': '<PERSON> has wide feet', 'valid_at': '2024-08-20T00:01:00Z', 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'PURCHASED', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': 'b30e3ba27aa14f88895156331a435237', 'fact': \"<PERSON> bought the Men's Couriers shoes\", 'valid_at': '2024-07-30T00:05:00Z', 'invalid_at': None}, {'relation_type': 'CAUSES_DISCOMFORT', 'source_node_uuid': '8b43988e689b437095c7e75aa1044490', 'target_node_uuid': 'b30e3ba27aa14f88895156331a435237', 'fact': \"<PERSON>'s wide feet cause discomfort with the Men's Couriers shoes\", 'valid_at': '2024-08-20T00:01:00Z', 'invalid_at': None}, {'relation_type': 'HAS_CHARACTERISTIC', 'source_node_uuid': 'c4091c3ffc814f2c9017304361898585', 'target_node_uuid': '8b43988e689b437095c7e75aa1044490', 'fact': 'John has wide feet', 'valid_at': '2024-08-20T00:01:00Z', 'invalid_at': None}] in 4484.************ ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: PURCHASED from (UUID: c4091c3ffc814f2c9017304361898585) to (UUID: b30e3ba27aa14f88895156331a435237)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: CAUSES_DISCOMFORT from (UUID: 8b43988e689b437095c7e75aa1044490) to (UUID: b30e3ba27aa14f88895156331a435237)\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: HAS_CHARACTERISTIC from (UUID: c4091c3ffc814f2c9017304361898585) to (UUID: 8b43988e689b437095c7e75aa1044490)\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded John has wide feet in 0.1614089012145996 ms\n", "graphiti_core.edges - INFO - embedded <PERSON> bought the Men's Couriers shoes in 0.171356201171875 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded <PERSON>'s wide feet cause discomfort with the Men's Couriers shoes in 0.2485518455505371 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant edges: {'199ec767d52c47d2a5965f3197b1c4d2', '2a9cf189e19649c19ec127c4024cfe51', 'df1d2e82a40e40e1b3734c2298774a6b', '4721330c8f2b45e69e07f520773f8794', 'f6300668591242d3a64d94bf9de7d4bc', '941c96b8d086467fa1cbe6b0f6481604', 'e4cd07dfddc84072985aa8cf4e1dc01b', '6a19ae37d5074d808d4f951ab347e2b1', '518d5ef539004ceca7b9b9a750e22bd4'} in 25.846004486083984 ms\n", "graphiti_core.graphiti - INFO - Existing edges: [('PURCHASES', '199ec767d52c47d2a5965f3197b1c4d2'), ('RECOMMENDS', '4721330c8f2b45e69e07f520773f8794'), ('INTERESTED_IN', '2a9cf189e19649c19ec127c4024cfe51'), ('HAS_SHOE_SIZE', '6a19ae37d5074d808d4f951ab347e2b1'), ('LIKES', 'df1d2e82a40e40e1b3734c2298774a6b'), ('BELONGS_TO_CATEGORY', 'f6300668591242d3a64d94bf9de7d4bc'), ('HAS_STYLE', '941c96b8d086467fa1cbe6b0f6481604'), ('IS_ALLERGIC_TO', 'e4cd07dfddc84072985aa8cf4e1dc01b'), ('ASSISTS', '518d5ef539004ceca7b9b9a750e22bd4')]\n", "graphiti_core.graphiti - INFO - Extracted edges: [('PURCHASED', '50f7bed00d744774b33e29cb70f686d3'), ('CAUSES_DISCOMFORT', '1055fb8279af4c4c8c3fb78350d610d0'), ('HAS_CHARACTERISTIC', 'aa657e8bcb9446e19552f99a1c2299d8')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted unique edges: [{'uuid': '1055fb8279af4c4c8c3fb78350d610d0'}, {'uuid': 'aa657e8bcb9446e19552f99a1c2299d8'}]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the timestamp of <PERSON>'s message where he mentions the discomfort caused by the shoes. This is when the relationship 'CAUSES_DISCOMFORT' is first established in the conversation. There is no information about when this relationship ends, so invalid_at is set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact '<PERSON> has wide feet' is a characteristic that is not associated with any specific date in the given conversation. It appears to be an ongoing trait of <PERSON>'<PERSON>, and there is no information provided about when this characteristic was established or changed. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to 2024-07-30T00:05:00Z because that's when <PERSON> confirmed the purchase by saying 'Blue is great! Love the look. I'll take them.' in response to the SalesBot's offer. There is no information about when or if the purchase relationship ended, so invalid_at is set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the timestamp when SalesBot recommended the Men's Couriers shoes to the customer, as seen in the previous episodes. There is no information about when this recommendation became invalid, so invalid_at is set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact '<PERSON> is looking for a new pair of shoes' does not contain any specific temporal information about when this interest began or ended. The conversation provides context about <PERSON>'s recent purchase and return of shoes, but it doesn't directly establish when <PERSON>'s general interest in shoes started or stopped. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact '<PERSON>'s shoe size is 10' does not contain any temporal information about when this relationship was established or changed. The conversation provides no specific dates related to <PERSON>'s shoe size. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to 2024-07-30T00:05:00Z because that's when <PERSON> expressed his liking for the blue color in the conversation. The invalid_at is null as there's no information indicating when or if this preference changed.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'The Anytime No Show Sock - Rugged Beige belongs to the Socks category' does not contain any temporal information about when this relationship was established or changed. The conversation and provided context also do not offer any relevant dates for this specific categorization. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'Men's Couriers - Natural Black/Basin Blue (Blizzard Sole) has a Runner style' does not contain any temporal information about when this style relationship was established or changed. The conversation provides no specific dates related to the product's style. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact '<PERSON> is allergic to wool' does not contain any temporal information about when this allergy was established or changed. The conversation provided does not mention anything about <PERSON>'s wool allergy or its onset. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the start of the day when SalesBot offers assistance to <PERSON> in the current episode. The invalid_at is null as there's no information about when this assistance relationship ends.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Invalidated edge: PURCHASES (UUID: 199ec767d52c47d2a5965f3197b1c4d2). Updated Fact: <PERSON> purchased the Men's Couriers shoes but later decided to return them due to discomfort caused by his wide feet\n", "graphiti_core.graphiti - INFO - Invalidated edges: [('PURCHASES', '199ec767d52c47d2a5965f3197b1c4d2')]\n", "graphiti_core.graphiti - INFO - Edge touched nodes: [('<PERSON>', 'c4091c3ffc814f2c9017304361898585'), (\"Men's Couriers\", 'b30e3ba27aa14f88895156331a435237'), ('Wide Feet', '8b43988e689b437095c7e75aa1044490')]\n", "graphiti_core.graphiti - INFO - Deduped edges: [('CAUSES_DISCOMFORT', '1055fb8279af4c4c8c3fb78350d610d0'), ('HAS_CHARACTERISTIC', 'aa657e8bcb9446e19552f99a1c2299d8')]\n", "graphiti_core.graphiti - INFO - Built episodic edges: [EpisodicEdge(uuid='0442743601b44820b4abc6d1a5936e0a', source_node_uuid='37c0e9ecaa424caea59854d1d8c2c756', target_node_uuid='c4091c3ffc814f2c9017304361898585', created_at=datetime.datetime(2024, 8, 31, 11, 37, 27, 513372)), EpisodicEdge(uuid='a1ecce43576642ff8397f3c17d7767c6', source_node_uuid='37c0e9ecaa424caea59854d1d8c2c756', target_node_uuid='b30e3ba27aa14f88895156331a435237', created_at=datetime.datetime(2024, 8, 31, 11, 37, 27, 513372)), EpisodicEdge(uuid='77d0a0f354e94bf1ba020aec3972a422', source_node_uuid='37c0e9ecaa424caea59854d1d8c2c756', target_node_uuid='8b43988e689b437095c7e75aa1044490', created_at=datetime.datetime(2024, 8, 31, 11, 37, 27, 513372))]\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 37c0e9ecaa424caea59854d1d8c2c756\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: c4091c3ffc814f2c9017304361898585\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: b30e3ba27aa14f88895156331a435237\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 8b43988e689b437095c7e75aa1044490\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 0442743601b44820b4abc6d1a5936e0a\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 77d0a0f354e94bf1ba020aec3972a422\n", "graphiti_core.edges - INFO - Saved edge to neo4j: a1ecce43576642ff8397f3c17d7767c6\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 4721330c8f2b45e69e07f520773f8794\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 2a9cf189e19649c19ec127c4024cfe51\n", "graphiti_core.edges - INFO - Saved edge to neo4j: df1d2e82a40e40e1b3734c2298774a6b\n", "graphiti_core.edges - INFO - Saved edge to neo4j: f6300668591242d3a64d94bf9de7d4bc\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 941c96b8d086467fa1cbe6b0f6481604\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 518d5ef539004ceca7b9b9a750e22bd4\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 6a19ae37d5074d808d4f951ab347e2b1\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 1055fb8279af4c4c8c3fb78350d610d0\n", "graphiti_core.edges - INFO - Saved edge to neo4j: e4cd07dfddc84072985aa8cf4e1dc01b\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 199ec767d52c47d2a5965f3197b1c4d2\n", "graphiti_core.edges - INFO - Saved edge to neo4j: aa657e8bcb9446e19552f99a1c2299d8\n", "graphiti_core.graphiti - INFO - Completed add_episode in 47468.27507019043 ms\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Extracted new nodes: [{'name': 'SalesBot', 'labels': ['Entity', 'Speaker', '<PERSON><PERSON>'], 'summary': 'AI sales assistant handling customer service'}, {'name': 'Return', 'labels': ['Entity', 'Process'], 'summary': 'The process of returning a purchased item'}] in 2003.1559467315674 ms\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: SalesBot (UUID: d0142efc981e4240a9d30da2ffe7475d)\n", "graphiti_core.utils.maintenance.node_operations - INFO - Created new node: Return (UUID: 821b0a3cefcc4b798910dc712edae703)\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('SalesBot', 'd0142efc981e4240a9d30da2ffe7475d'), ('Return', '821b0a3cefcc4b798910dc712edae703')]\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded Return in 0.1762232780456543 ms\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.nodes - INFO - embedded SalesBot in 0.23417210578918457 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant nodes: {'95066726921c4e5883a86d8095cd7e0a', '8b43988e689b437095c7e75aa1044490', 'ccd7590b3601440f9ae816507da79130', '24c2e745740c4ba8bc75e60f51cf2865', 'e4cadcacd02f42e4b620721dba42bc9a', '0b63349f5a3342f1a87be29f316300f1', 'c4efdae7ab9240fd8b8f59ac741a19bf', 'd362076a1e584227bcf51239914e39ad', '7d49a3b6bb4249f7a1262fbfbe6386b0', 'a06d832a07fc403f8e43df6b2b650f1a'} in 42.6788330078125 ms\n", "graphiti_core.graphiti - INFO - Extracted nodes: [('SalesBot', 'd0142efc981e4240a9d30da2ffe7475d'), ('Return', '821b0a3cefcc4b798910dc712edae703')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.node_operations - INFO - Deduplicated nodes: [{'name': 'SalesBot', 'duplicate_of': 'SalesBot'}] in 1072.2811222076416 ms\n", "graphiti_core.graphiti - INFO - Adjusted touched nodes: [('SalesBot', 'd362076a1e584227bcf51239914e39ad'), ('Return', '821b0a3cefcc4b798910dc712edae703')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "{'edges': [{'relation_type': 'HANDLES', 'source_node_uuid': 'd362076a1e584227bcf51239914e39ad', 'target_node_uuid': '821b0a3cefcc4b798910dc712edae703', 'fact': 'SalesBot processes returns for customers', 'valid_at': '2024-08-20T00:02:00Z', 'invalid_at': None}]}\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted new edges: [{'relation_type': 'HANDLES', 'source_node_uuid': 'd362076a1e584227bcf51239914e39ad', 'target_node_uuid': '821b0a3cefcc4b798910dc712edae703', 'fact': 'SalesBot processes returns for customers', 'valid_at': '2024-08-20T00:02:00Z', 'invalid_at': None}] in 1752.0487308502197 ms\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Created new edge: HANDLES from (UUID: d362076a1e584227bcf51239914e39ad) to (UUID: 821b0a3cefcc4b798910dc712edae703)\n", "httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.edges - INFO - embedded SalesBot processes returns for customers in 0.16264009475708008 ms\n", "graphiti_core.search.search_utils - INFO - Found relevant edges: {'518d5ef539004ceca7b9b9a750e22bd4', '4721330c8f2b45e69e07f520773f8794', '1086271667484ba2aa579eaa2d69dab8', '1a824bf8d9a54f47ba6cbb9265239c28'} in 21.453142166137695 ms\n", "graphiti_core.graphiti - INFO - Existing edges: [('WORKS_FOR', '1a824bf8d9a54f47ba6cbb9265239c28'), ('ASSISTS', '518d5ef539004ceca7b9b9a750e22bd4'), ('RECOMMENDS', '4721330c8f2b45e69e07f520773f8794'), ('INQUIRES_ABOUT', '1086271667484ba2aa579eaa2d69dab8')]\n", "graphiti_core.graphiti - INFO - Extracted edges: [('HANDLES', 'c9ba0d6539664c6d8c9b4cb42be28b92')]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.edge_operations - INFO - Extracted unique edges: [{'uuid': 'c9ba0d6539664c6d8c9b4cb42be28b92'}]\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'SalesBot processes returns for customers' does not contain any specific temporal information about when this relationship was established or changed. The conversation provides an example of SalesBot handling a return, but it doesn't indicate when this capability was introduced or if it has changed. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any specific temporal information about when SalesBot started or stopped working for ManyBirds. The fact only states that SalesBot is an AI assistant designed to help customers of ManyBirds, without mentioning any dates related to the establishment or change of this relationship.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The valid_at date is set to the timestamp of the current episode where SalesBot offers assistance to <PERSON>. The invalid_at is null because there's no information about when this assistance ends.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact does not contain any specific temporal information about when SalesBot recommended the Men's Couriers shoes to the customer. The conversation provides no direct dates or times for this recommendation event. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.utils.maintenance.temporal_operations - INFO - Edge date extraction explanation: The edge fact 'SalesBot asks about the material of shoes the customer is looking for' does not contain any temporal information. The conversation provided does not mention any dates related to when this inquiry was made or when it might have ended. Therefore, both valid_at and invalid_at are set to null.\n", "httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "graphiti_core.graphiti - INFO - Invalidated edges: []\n", "graphiti_core.graphiti - INFO - Edge touched nodes: [('SalesBot', 'd362076a1e584227bcf51239914e39ad'), ('Return', '821b0a3cefcc4b798910dc712edae703')]\n", "graphiti_core.graphiti - INFO - Deduped edges: [('HANDLES', 'c9ba0d6539664c6d8c9b4cb42be28b92')]\n", "graphiti_core.graphiti - INFO - Built episodic edges: [EpisodicEdge(uuid='45a02863ca5c4a248a11762033533088', source_node_uuid='d02afd3c895647b9a67eebeb7501c77a', target_node_uuid='d362076a1e584227bcf51239914e39ad', created_at=datetime.datetime(2024, 8, 31, 11, 38, 14, 980001)), EpisodicEdge(uuid='f67c96c4f8824bb7bbb2ff21b43d2141', source_node_uuid='d02afd3c895647b9a67eebeb7501c77a', target_node_uuid='821b0a3cefcc4b798910dc712edae703', created_at=datetime.datetime(2024, 8, 31, 11, 38, 14, 980001))]\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: d02afd3c895647b9a67eebeb7501c77a\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: d362076a1e584227bcf51239914e39ad\n", "graphiti_core.nodes - INFO - Saved Node to neo4j: 821b0a3cefcc4b798910dc712edae703\n", "graphiti_core.edges - INFO - Saved edge to neo4j: f67c96c4f8824bb7bbb2ff21b43d2141\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 45a02863ca5c4a248a11762033533088\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 1a824bf8d9a54f47ba6cbb9265239c28\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 518d5ef539004ceca7b9b9a750e22bd4\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 1086271667484ba2aa579eaa2d69dab8\n", "graphiti_core.edges - INFO - Saved edge to neo4j: c9ba0d6539664c6d8c9b4cb42be28b92\n", "graphiti_core.edges - INFO - Saved edge to neo4j: 4721330c8f2b45e69e07f520773f8794\n", "graphiti_core.graphiti - INFO - Completed add_episode in 16244.968175888062 ms\n"]}], "source": ["await add_messages(client, shoe_conversation_2, prefix='conversation-2')"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.search.search - INFO - search returned context for query What shoes has <PERSON> purchased? in 215.75593948364258 ms\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'199ec767d52c47d2a5965f3197b1c4d2'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'b30e3ba27aa14f88895156331a435237'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">36</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">42</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">827088</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'PURCHASES'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">\"<PERSON> purchased the Men's Couriers shoes but later decided to return them due to discomfort caused by his wide feet\"</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'4c8afb4aa1b446899a85249df475bc66'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">38</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">818497</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span>, <span style=\"color: #808000; text-decoration-color: #808000\">tzinfo</span>=<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">UTC</span><span style=\"font-weight: bold\">&gt;)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">}</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'2a9cf189e19649c19ec127c4024cfe51'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'77f8b23b74014a7f85fffa0067dbf815'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">34</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">57</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">412667</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'INTERESTED_IN'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON> is looking for a new pair of shoes'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'c2ebc79d2a204efb845be84b6dbf69d7'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">}</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'aa657e8bcb9446e19552f99a1c2299d8'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'8b43988e689b437095c7e75aa1044490'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">37</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">39</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">665400</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'HAS_CHARACTERISTIC'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON> has wide feet'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'37c0e9ecaa424caea59854d1d8c2c756'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'199ec767d52c47d2a5965f3197b1c4d2'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'b30e3ba27aa14f88895156331a435237'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m36\u001b[0m, \u001b[1;36m42\u001b[0m, \u001b[1;36m827088\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'PURCHASES'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m\"<PERSON> purchased the Men's Couriers shoes but later decided to return them due to discomfort caused by his wide feet\"\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'4c8afb4aa1b446899a85249df475bc66'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m38\u001b[0m, \u001b[1;36m14\u001b[0m, \u001b[1;36m818497\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m7\u001b[0m, \u001b[1;36m30\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m5\u001b[0m, \u001b[33mtzinfo\u001b[0m=\u001b[1m<\u001b[0m\u001b[1;95mUTC\u001b[0m\u001b[1m>\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m}\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'2a9cf189e19649c19ec127c4024cfe51'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'77f8b23b74014a7f85fffa0067dbf815'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m34\u001b[0m, \u001b[1;36m57\u001b[0m, \u001b[1;36m412667\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'INTERESTED_IN'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m'<PERSON> is looking for a new pair of shoes'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'c2ebc79d2a204efb845be84b6dbf69d7'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m}\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'aa657e8bcb9446e19552f99a1c2299d8'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'8b43988e689b437095c7e75aa1044490'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m37\u001b[0m, \u001b[1;36m39\u001b[0m, \u001b[1;36m665400\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'HAS_CHARACTERISTIC'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m'<PERSON> has wide feet'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'37c0e9ecaa424caea59854d1d8c2c756'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["r = await client.search('What shoes has <PERSON> purchased?', center_node_uuid=john_uuid, num_results=3)\n", "\n", "pretty_print(r)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.search.search - INFO - search returned context for query What shoes has <PERSON> purchased? in 231.48012161254883 ms\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'199ec767d52c47d2a5965f3197b1c4d2'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'b30e3ba27aa14f88895156331a435237'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">36</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">42</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">827088</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'PURCHASES'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">\"<PERSON> purchased the Men's Couriers shoes but later decided to return them due to discomfort caused by his wide feet\"</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'4c8afb4aa1b446899a85249df475bc66'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">38</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">818497</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span>, <span style=\"color: #808000; text-decoration-color: #808000\">tzinfo</span>=<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">UTC</span><span style=\"color: #000000; text-decoration-color: #000000\">&gt;</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">)</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">}</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'2a9cf189e19649c19ec127c4024cfe51'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'77f8b23b74014a7f85fffa0067dbf815'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">34</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">57</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">412667</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">)</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'INTERESTED_IN'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON> is looking for a new pair of shoes'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'c2ebc79d2a204efb845be84b6dbf69d7'</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">]</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">}</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'aa657e8bcb9446e19552f99a1c2299d8'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'8b43988e689b437095c7e75aa1044490'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">37</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">39</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">665400</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">)</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'HAS_CHARACTERISTIC'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON> has wide feet'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'37c0e9ecaa424caea59854d1d8c2c756'</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">]</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">}</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'df1d2e82a40e40e1b3734c2298774a6b'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'588989497641456fb33243f035731f98'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">36</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">42</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">828745</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">)</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'LIKES'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON> expresses that he likes the Basin Blue color for the shoes'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'4c8afb4aa1b446899a85249df475bc66'</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">]</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #808000; text-decoration-color: #808000\">tzinfo</span><span style=\"color: #000000; text-decoration-color: #000000\">=&lt;UTC</span><span style=\"font-weight: bold\">&gt;)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">}</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'6a19ae37d5074d808d4f951ab347e2b1'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'fcea4a4539244cd28aac1bb11def0cab'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">35</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">44</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">738829</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'HAS_SHOE_SIZE'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">\"<PERSON>'s shoe size is 10\"</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'6b41a387ca504a2686b636a20b5673a3'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'199ec767d52c47d2a5965f3197b1c4d2'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'b30e3ba27aa14f88895156331a435237'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m36\u001b[0m, \u001b[1;36m42\u001b[0m, \u001b[1;36m827088\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'PURCHASES'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m\"<PERSON> purchased the Men's Couriers shoes but later decided to return them due to discomfort caused by his wide feet\"\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'4c8afb4aa1b446899a85249df475bc66'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m38\u001b[0m, \u001b[1;36m14\u001b[0m, \u001b[1;36m818497\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m7\u001b[0m, \u001b[1;36m30\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m5\u001b[0m, \u001b[33mtzinfo\u001b[0m=\u001b[1m<\u001b[0m\u001b[1;95mUTC\u001b[0m\u001b[39m>\u001b[0m\u001b[1;39m)\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m}\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m{\u001b[0m\n", "\u001b[2;32m│   <PERSON>   \u001b[0m\u001b[32m'uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'2a9cf189e19649c19ec127c4024cfe51'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'77f8b23b74014a7f85fffa0067dbf815'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;35mdatetime.datetime\u001b[0m\u001b[1;39m(\u001b[0m\u001b[1;36m2024\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m8\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m31\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m11\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m34\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m57\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m412667\u001b[0m\u001b[1;39m)\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'INTERESTED_IN'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'John is looking for a new pair of shoes'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;39m[\u001b[0m\u001b[32m'c2ebc79d2a204efb845be84b6dbf69d7'\u001b[0m\u001b[1;39m]\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m}\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m{\u001b[0m\n", "\u001b[2;32m│   <PERSON>   \u001b[0m\u001b[32m'uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'aa657e8bcb9446e19552f99a1c2299d8'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'8b43988e689b437095c7e75aa1044490'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;35mdatetime.datetime\u001b[0m\u001b[1;39m(\u001b[0m\u001b[1;36m2024\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m8\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m31\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m11\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m37\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m39\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m665400\u001b[0m\u001b[1;39m)\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'HAS_CHARACTERISTIC'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'<PERSON> has wide feet'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;39m[\u001b[0m\u001b[32m'37c0e9ecaa424caea59854d1d8c2c756'\u001b[0m\u001b[1;39m]\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m}\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m{\u001b[0m\n", "\u001b[2;32m│   <PERSON>   \u001b[0m\u001b[32m'uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'df1d2e82a40e40e1b3734c2298774a6b'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'588989497641456fb33243f035731f98'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;35mdatetime.datetime\u001b[0m\u001b[1;39m(\u001b[0m\u001b[1;36m2024\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m8\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m31\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m11\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m36\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m42\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m828745\u001b[0m\u001b[1;39m)\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'LIKES'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m<PERSON>   <PERSON>   \u001b[0m\u001b[32m'fact'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'<PERSON> expresses that he likes the Basin Blue color for the shoes'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;39m[\u001b[0m\u001b[32m'4c8afb4aa1b446899a85249df475bc66'\u001b[0m\u001b[1;39m]\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;35mdatetime.datetime\u001b[0m\u001b[1;39m(\u001b[0m\u001b[1;36m2024\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m7\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m30\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m0\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m5\u001b[0m\u001b[39m, \u001b[0m\u001b[33mtzinfo\u001b[0m\u001b[39m=<UTC\u001b[0m\u001b[1m>\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m}\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'6a19ae37d5074d808d4f951ab347e2b1'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'fcea4a4539244cd28aac1bb11def0cab'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m35\u001b[0m, \u001b[1;36m44\u001b[0m, \u001b[1;36m738829\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'HAS_SHOE_SIZE'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m\"<PERSON>'s shoe size is 10\"\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'6b41a387ca504a2686b636a20b5673a3'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["r = await client.search('What shoes has <PERSON> purchased?', center_node_uuid=john_uuid, num_results=5)\n", "\n", "pretty_print(r)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.search.search - INFO - search returned context for query Who is <PERSON>? in 211.70878410339355 ms\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'e4cd07dfddc84072985aa8cf4e1dc01b'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'ccd7590b3601440f9ae816507da79130'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">35</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">44</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">738205</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'IS_ALLERGIC_TO'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON> is allergic to wool'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'6b41a387ca504a2686b636a20b5673a3'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">}</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'aa657e8bcb9446e19552f99a1c2299d8'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'8b43988e689b437095c7e75aa1044490'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">37</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">39</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">665400</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'HAS_CHARACTERISTIC'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON> has wide feet'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'37c0e9ecaa424caea59854d1d8c2c756'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">}</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'6a19ae37d5074d808d4f951ab347e2b1'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'fcea4a4539244cd28aac1bb11def0cab'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">35</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">44</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">738829</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'HAS_SHOE_SIZE'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">\"<PERSON>'s shoe size is 10\"</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'6b41a387ca504a2686b636a20b5673a3'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">}</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'518d5ef539004ceca7b9b9a750e22bd4'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'d362076a1e584227bcf51239914e39ad'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">37</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">15</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">423989</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'ASSISTS'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'SalesBot offers assistance to <PERSON>'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'7087342bfe86423bb702060fa9cc612b'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">20</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>, <span style=\"color: #808000; text-decoration-color: #808000\">tzinfo</span>=<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">UTC</span><span style=\"font-weight: bold\">&gt;)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">}</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'2a9cf189e19649c19ec127c4024cfe51'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'77f8b23b74014a7f85fffa0067dbf815'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">34</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">57</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">412667</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'INTERESTED_IN'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON> is looking for a new pair of shoes'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'c2ebc79d2a204efb845be84b6dbf69d7'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'e4cd07dfddc84072985aa8cf4e1dc01b'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'ccd7590b3601440f9ae816507da79130'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m35\u001b[0m, \u001b[1;36m44\u001b[0m, \u001b[1;36m738205\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'IS_ALLERGIC_TO'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m'<PERSON> is allergic to wool'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'6b41a387ca504a2686b636a20b5673a3'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m}\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'aa657e8bcb9446e19552f99a1c2299d8'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'8b43988e689b437095c7e75aa1044490'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m37\u001b[0m, \u001b[1;36m39\u001b[0m, \u001b[1;36m665400\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'HAS_CHARACTERISTIC'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m'<PERSON> has wide feet'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'37c0e9ecaa424caea59854d1d8c2c756'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m}\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'6a19ae37d5074d808d4f951ab347e2b1'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'fcea4a4539244cd28aac1bb11def0cab'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m35\u001b[0m, \u001b[1;36m44\u001b[0m, \u001b[1;36m738829\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'HAS_SHOE_SIZE'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m\"<PERSON>'s shoe size is 10\"\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'6b41a387ca504a2686b636a20b5673a3'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m}\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'518d5ef539004ceca7b9b9a750e22bd4'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'd362076a1e584227bcf51239914e39ad'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m37\u001b[0m, \u001b[1;36m15\u001b[0m, \u001b[1;36m423989\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'ASSISTS'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m'SalesBot offers assistance to <PERSON>'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'7087342bfe86423bb702060fa9cc612b'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m20\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m2\u001b[0m, \u001b[33mtzinfo\u001b[0m=\u001b[1m<\u001b[0m\u001b[1;95mUTC\u001b[0m\u001b[1m>\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m}\u001b[0m,\n", "\u001b[2;32m│   \u001b[0m\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'2a9cf189e19649c19ec127c4024cfe51'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'77f8b23b74014a7f85fffa0067dbf815'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m34\u001b[0m, \u001b[1;36m57\u001b[0m, \u001b[1;36m412667\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'INTERESTED_IN'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m'<PERSON> is looking for a new pair of shoes'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'c2ebc79d2a204efb845be84b6dbf69d7'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["r = await client.search('Who is <PERSON>?', num_results=5)\n", "\n", "pretty_print(r)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "graphiti_core.search.search - INFO - search returned context for query What did <PERSON> do about his discomfort with the Mens Couriers shoes in 215.81482887268066 ms\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1055fb8279af4c4c8c3fb78350d610d0'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'8b43988e689b437095c7e75aa1044490'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'b30e3ba27aa14f88895156331a435237'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">37</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">39</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">664102</span><span style=\"font-weight: bold\">)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'CAUSES_DISCOMFORT'</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">\"<PERSON>'s wide feet cause discomfort with the Men's Couriers shoes\"</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'37c0e9ecaa424caea59854d1d8c2c756'</span><span style=\"font-weight: bold\">]</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">20</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #808000; text-decoration-color: #808000\">tzinfo</span>=<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">UTC</span><span style=\"color: #000000; text-decoration-color: #000000\">&gt;</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">)</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">}</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'199ec767d52c47d2a5965f3197b1c4d2'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'b30e3ba27aa14f88895156331a435237'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">36</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">42</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">827088</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">)</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'PURCHASES'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">\"<PERSON> purchased the Men's Couriers shoes but later decided to return them due to discomfort caused by his wide feet\"</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'4c8afb4aa1b446899a85249df475bc66'</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">]</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">38</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">818497</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">)</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #808000; text-decoration-color: #808000\">tzinfo</span><span style=\"color: #000000; text-decoration-color: #000000\">=&lt;UTC&gt;</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">)</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">}</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'2a9cf189e19649c19ec127c4024cfe51'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'77f8b23b74014a7f85fffa0067dbf815'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">34</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">57</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">412667</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">)</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'INTERESTED_IN'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON> is looking for a new pair of shoes'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'c2ebc79d2a204efb845be84b6dbf69d7'</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">]</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">}</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'4721330c8f2b45e69e07f520773f8794'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'d362076a1e584227bcf51239914e39ad'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'ed9688ba1e9940ff87d3e26bcf5d7ae4'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">36</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">12</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">540437</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">)</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'RECOMMENDS'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">\"SalesBot recommends Men's Couriers shoes to the customer\"</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'e7c29d5d38854cac801bc07d236240a8'</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">]</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">}</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">{</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'df1d2e82a40e40e1b3734c2298774a6b'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'source_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'c4091c3ffc814f2c9017304361898585'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'target_node_uuid'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'588989497641456fb33243f035731f98'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'created_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">36</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">42</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">828745</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">)</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'name'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'LIKES'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'fact'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON> expresses that he likes the Basin Blue color for the shoes'</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'episodes'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'4c8afb4aa1b446899a85249df475bc66'</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">]</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'expired_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span><span style=\"color: #000000; text-decoration-color: #000000\">,</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'valid_at'</span><span style=\"color: #000000; text-decoration-color: #000000\">: </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">datetime.datetime</span><span style=\"color: #000000; text-decoration-color: #000000; font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span><span style=\"color: #000000; text-decoration-color: #000000\">, </span><span style=\"color: #808000; text-decoration-color: #808000\">tzinfo</span><span style=\"color: #000000; text-decoration-color: #000000\">=&lt;UTC</span><span style=\"font-weight: bold\">&gt;)</span>,\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   │   </span><span style=\"color: #008000; text-decoration-color: #008000\">'invalid_at'</span>: <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"color: #7fbf7f; text-decoration-color: #7fbf7f\">│   </span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m{\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'uuid'\u001b[0m: \u001b[32m'1055fb8279af4c4c8c3fb78350d610d0'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m: \u001b[32m'8b43988e689b437095c7e75aa1044490'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m: \u001b[32m'b30e3ba27aa14f88895156331a435237'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m31\u001b[0m, \u001b[1;36m11\u001b[0m, \u001b[1;36m37\u001b[0m, \u001b[1;36m39\u001b[0m, \u001b[1;36m664102\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m: \u001b[32m'CAUSES_DISCOMFORT'\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m: \u001b[32m\"<PERSON>'s wide feet cause discomfort with the Men's Couriers shoes\"\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'37c0e9ecaa424caea59854d1d8c2c756'\u001b[0m\u001b[1m]\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m: \u001b[3;35mNone\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m: \u001b[1;35mdatetime.datetime\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m2024\u001b[0m, \u001b[1;36m8\u001b[0m, \u001b[1;36m20\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[33mtzinfo\u001b[0m=\u001b[1m<\u001b[0m\u001b[1;95mUTC\u001b[0m\u001b[39m>\u001b[0m\u001b[1;39m)\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m}\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m{\u001b[0m\n", "\u001b[2;32m│   <PERSON>   \u001b[0m\u001b[32m'uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'199ec767d52c47d2a5965f3197b1c4d2'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'b30e3ba27aa14f88895156331a435237'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;35mdatetime.datetime\u001b[0m\u001b[1;39m(\u001b[0m\u001b[1;36m2024\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m8\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m31\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m11\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m36\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m42\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m827088\u001b[0m\u001b[1;39m)\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'PURCHASES'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m\"John purchased the Men's Couriers shoes but later decided to return them due to discomfort caused by his wide feet\"\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;39m[\u001b[0m\u001b[32m'4c8afb4aa1b446899a85249df475bc66'\u001b[0m\u001b[1;39m]\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;35mdatetime.datetime\u001b[0m\u001b[1;39m(\u001b[0m\u001b[1;36m2024\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m8\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m31\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m11\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m38\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m14\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m818497\u001b[0m\u001b[1;39m)\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;35mdatetime.datetime\u001b[0m\u001b[1;39m(\u001b[0m\u001b[1;36m2024\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m7\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m30\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m0\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m5\u001b[0m\u001b[39m, \u001b[0m\u001b[33mtzinfo\u001b[0m\u001b[39m=<UTC>\u001b[0m\u001b[1;39m)\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m}\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m{\u001b[0m\n", "\u001b[2;32m│   <PERSON>   \u001b[0m\u001b[32m'uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'2a9cf189e19649c19ec127c4024cfe51'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'77f8b23b74014a7f85fffa0067dbf815'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;35mdatetime.datetime\u001b[0m\u001b[1;39m(\u001b[0m\u001b[1;36m2024\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m8\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m31\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m11\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m34\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m57\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m412667\u001b[0m\u001b[1;39m)\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'INTERESTED_IN'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'John is looking for a new pair of shoes'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;39m[\u001b[0m\u001b[32m'c2ebc79d2a204efb845be84b6dbf69d7'\u001b[0m\u001b[1;39m]\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m}\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m{\u001b[0m\n", "\u001b[2;32m│   <PERSON>   \u001b[0m\u001b[32m'uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'4721330c8f2b45e69e07f520773f8794'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'd362076a1e584227bcf51239914e39ad'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'ed9688ba1e9940ff87d3e26bcf5d7ae4'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;35mdatetime.datetime\u001b[0m\u001b[1;39m(\u001b[0m\u001b[1;36m2024\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m8\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m31\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m11\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m36\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m12\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m540437\u001b[0m\u001b[1;39m)\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'RECOMMENDS'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'fact'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m\"SalesBot recommends Men's Couriers shoes to the customer\"\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;39m[\u001b[0m\u001b[32m'e7c29d5d38854cac801bc07d236240a8'\u001b[0m\u001b[1;39m]\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m}\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1;39m{\u001b[0m\n", "\u001b[2;32m│   <PERSON>   \u001b[0m\u001b[32m'uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'df1d2e82a40e40e1b3734c2298774a6b'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'source_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'c4091c3ffc814f2c9017304361898585'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'target_node_uuid'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'588989497641456fb33243f035731f98'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'created_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;35mdatetime.datetime\u001b[0m\u001b[1;39m(\u001b[0m\u001b[1;36m2024\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m8\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m31\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m11\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m36\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m42\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m828745\u001b[0m\u001b[1;39m)\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'name'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'LIKES'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m<PERSON>   <PERSON>   \u001b[0m\u001b[32m'fact'\u001b[0m\u001b[39m: \u001b[0m\u001b[32m'<PERSON> expresses that he likes the Basin Blue color for the shoes'\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'episodes'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;39m[\u001b[0m\u001b[32m'4c8afb4aa1b446899a85249df475bc66'\u001b[0m\u001b[1;39m]\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'expired_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[3;35mNone\u001b[0m\u001b[39m,\u001b[0m\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'valid_at'\u001b[0m\u001b[39m: \u001b[0m\u001b[1;35mdatetime.datetime\u001b[0m\u001b[1;39m(\u001b[0m\u001b[1;36m2024\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m7\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m30\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m0\u001b[0m\u001b[39m, \u001b[0m\u001b[1;36m5\u001b[0m\u001b[39m, \u001b[0m\u001b[33mtzinfo\u001b[0m\u001b[39m=<UTC\u001b[0m\u001b[1m>\u001b[0m\u001b[1m)\u001b[0m,\n", "\u001b[2;32m│   │   \u001b[0m\u001b[32m'invalid_at'\u001b[0m: \u001b[3;35mNone\u001b[0m\n", "\u001b[2;32m│   \u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["r = await client.search(\n", "    'What did <PERSON> do about his discomfort with the Mens Couriers shoes', num_results=5\n", ")\n", "\n", "pretty_print(r)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 4}